[BattleService] Player 10106021303 (可靠的.穿越者) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000053744 (AI_可靠的.穿越者) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000042038 (AI_可靠的.穿越者) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000076502 (AI_可靠的.穿越者) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleStateManager_431494384582666] Initialized
[PlayerManager_431494384582666] Player 10106021303 (可靠的.穿越者) Trophy 1 Health 3
[PlayerManager_431494384582666] Player 90000053744 (AI_可靠的.穿越者) Trophy 1 Health 3
[PlayerManager_431494384582666] Player 90000042038 (AI_可靠的.穿越者) Trophy 1 Health 3
[PlayerManager_431494384582666] Player 90000076502 (AI_可靠的.穿越者) Trophy 1 Health 3
[PlayerManager_431494384582666] Initialized with 4 players
[BattleInstanceManager] Initialized for battle 431494384582666
[OpponentPairManager] Initialized for battle 431494384582666
[BuffManager_431494384582666] Initialized
[CheckerBoard_431494384582666] Cleared checkerboard
[CheckerBoard_431494384582666] Initialized
[AutoChessScene_431494384582666] Event handlers registered
[AutoChessScene_431494384582666] Battle 431494384582666 initialized with 4 players, waiting for all players to enter
[SceneManager] Added AutoChessScene 431494384582666 to thread management
[BattleService] Battle 431494384582666 created successfully with 4 players
[BattleService] Battle 431494384582666 is now waiting for all players to call EnterBattle RPC
[BattleService] Current battle state: StateNone (waiting for player entry)
[BattleService] Players expected to enter: [10106021303, 90000053744, 90000042038, 90000076502]
[BattleService] ===== IMPORTANT: Battle will NOT start until ALL players call EnterBattle =====
[BattleService] ===== GameServer should call EnterBattle for each player after scene loading =====
[BattleService] ===== BOT STRATEGY: Player 10106021303 is real player, others are bots =====
[BattleService] Auto-entered bot player 90000053744 immediately
[BattleService] Auto-entered bot player 90000042038 immediately
[BattleService] Auto-entered bot player 90000076502 immediately
[BattleService] Battle 431494384582666 bots auto-entered: 3/4 players ready
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10106021303 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10106021303 entered battle 431494384582666 (initial), current count: 4
[BattleService] All 4 players entered battle 431494384582666, starting battle state machine
[BattleService] Entered players: [90000053744, 90000042038, 90000076502, 10106021303]
[BattleService] Triggering AutoChessScene.StartBattleStateMachine() for battle 431494384582666
[AutoChessScene_431494384582666] StartBattleStateMachine() called for battle 431494384582666
[AutoChessScene_431494384582666] BattleStateManager is ready, starting first round...
[AutoChessScene_431494384582666] Current state before starting: StateNone
[BattleStateManager_431494384582666] ===== STARTING NEW ROUND 1 =====
[BattleStateManager_431494384582666] Round 1 has buff selection: False
[BattleStateManager_431494384582666] Publishing RoundStartedEvent for round 1
[AutoChessScene_431494384582666] Round 1 started
[BattleStateManager_431494384582666] Setting state to StateRoundStart for round 1
[BattleStateManager_431494384582666] State: StateNone -> StateRoundStart (R1, 1000ms)
[AutoChessScene_431494384582666] HandleRoundStart: 4 active players
[AutoChessScene_431494384582666] Valid player: 10106021303, Health: 3
[AutoChessScene_431494384582666] Valid player: 90000053744, Health: 3
[AutoChessScene_431494384582666] Valid player: 90000042038, Health: 3
[AutoChessScene_431494384582666] Valid player: 90000076502, Health: 3
[AutoChessScene_431494384582666] No instance found for player 10106021303 when saving board data
[AutoChessScene_431494384582666] No instance found for player 90000053744 when saving board data
[AutoChessScene_431494384582666] No instance found for player 90000042038 when saving board data
[AutoChessScene_431494384582666] No instance found for player 90000076502 when saving board data
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player 90000053744 vs Player 90000042038
[OpponentPairManager] Random pair: Player 10106021303 vs Player 90000076502
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_431494384582666] Created 4 opponent pairs
[PlayerManager_431494384582666] Set player opponents, count: 4
[BattleInstanceManager] Created instance 431494384582666_1 for active players 90000053744 vs 90000042038
[CheckerBoard_431494384582666] Cleared checkerboard
[CheckerBoard_431494384582666] Initialized
[BattleInstance] 431494384582666_1 created with players: 90000053744, 90000042038
[BattleInstanceManager] Created instance 431494384582666_2 for active players 10106021303 vs 90000076502
[CheckerBoard_431494384582666] Cleared checkerboard
[CheckerBoard_431494384582666] Initialized
[BattleInstance] 431494384582666_2 created with players: 10106021303, 90000076502
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_431494384582666] Cleaned orphaned entities for player 10106021303
[AutoChessScene_431494384582666] Cleaned orphaned entities for player 90000053744
[AutoChessScene_431494384582666] Cleaned orphaned entities for player 90000042038
[AutoChessScene_431494384582666] Cleaned orphaned entities for player 90000076502
[PlayerManager_431494384582666] Reset all players ready status
[AutoChessScene_431494384582666] Round started with 2 battle instances
[AutoChessScene_431494384582666] Generating 5 heroes for all players in round 1
[CheckerBoard_431494384582666] Placed entity 1 at position (7, 4)
[CheckerBoard_431494384582666] Created entity ID:1 ConfigID:101 at (7, 4) for player 10106021303
[CheckerBoard_431494384582666] Placed entity 2 at position (8, 2)
[CheckerBoard_431494384582666] Created entity ID:2 ConfigID:101 at (8, 2) for player 10106021303
[CheckerBoard_431494384582666] Placed entity 3 at position (10, 1)
[CheckerBoard_431494384582666] Created entity ID:3 ConfigID:103 at (10, 1) for player 10106021303
[CheckerBoard_431494384582666] Placed entity 4 at position (8, 4)
[CheckerBoard_431494384582666] Created entity ID:4 ConfigID:102 at (8, 4) for player 10106021303
[CheckerBoard_431494384582666] Placed entity 5 at position (8, 5)
[CheckerBoard_431494384582666] Created entity ID:5 ConfigID:103 at (8, 5) for player 10106021303
[CheckerBoard_431494384582666] Generated 5/5 heroes for player 10106021303: 5 placed, 0 in temporary slots
[CheckerBoard_431494384582666] Generated 5 heroes for player 10106021303 in Enemy area
[AutoChessScene_431494384582666] Generated 5 heroes for player 10106021303: 5 placed on board, 0 in temporary slots
[CheckerBoard_431494384582666] Placed entity 1 at position (1, 5)
[CheckerBoard_431494384582666] Created entity ID:1 ConfigID:102 at (1, 5) for player 90000053744
[CheckerBoard_431494384582666] Placed entity 2 at position (4, 5)
[CheckerBoard_431494384582666] Created entity ID:2 ConfigID:103 at (4, 5) for player 90000053744
[CheckerBoard_431494384582666] Placed entity 3 at position (4, 1)
[CheckerBoard_431494384582666] Created entity ID:3 ConfigID:101 at (4, 1) for player 90000053744
[CheckerBoard_431494384582666] Placed entity 4 at position (3, 3)
[CheckerBoard_431494384582666] Created entity ID:4 ConfigID:101 at (3, 3) for player 90000053744
[CheckerBoard_431494384582666] Placed entity 5 at position (5, 1)
[CheckerBoard_431494384582666] Created entity ID:5 ConfigID:103 at (5, 1) for player 90000053744
[CheckerBoard_431494384582666] Generated 5/5 heroes for player 90000053744: 5 placed, 0 in temporary slots
[CheckerBoard_431494384582666] Generated 5 heroes for player 90000053744 in My area
[AutoChessScene_431494384582666] Generated 5 heroes for player 90000053744: 5 placed on board, 0 in temporary slots
[CheckerBoard_431494384582666] Placed entity 6 at position (2, 6)
[CheckerBoard_431494384582666] Created entity ID:6 ConfigID:101 at (2, 6) for player 90000042038
[CheckerBoard_431494384582666] Placed entity 7 at position (3, 5)
[CheckerBoard_431494384582666] Created entity ID:7 ConfigID:103 at (3, 5) for player 90000042038
[CheckerBoard_431494384582666] Placed entity 8 at position (2, 3)
[CheckerBoard_431494384582666] Created entity ID:8 ConfigID:102 at (2, 3) for player 90000042038
[CheckerBoard_431494384582666] Placed entity 9 at position (1, 4)
[CheckerBoard_431494384582666] Created entity ID:9 ConfigID:103 at (1, 4) for player 90000042038
[CheckerBoard_431494384582666] Placed entity 10 at position (5, 4)
[CheckerBoard_431494384582666] Created entity ID:10 ConfigID:101 at (5, 4) for player 90000042038
[CheckerBoard_431494384582666] Generated 5/5 heroes for player 90000042038: 5 placed, 0 in temporary slots
[CheckerBoard_431494384582666] Generated 5 heroes for player 90000042038 in My area
[AutoChessScene_431494384582666] Generated 5 heroes for player 90000042038: 5 placed on board, 0 in temporary slots
[CheckerBoard_431494384582666] Placed entity 6 at position (1, 5)
[CheckerBoard_431494384582666] Created entity ID:6 ConfigID:101 at (1, 5) for player 90000076502
[CheckerBoard_431494384582666] Placed entity 7 at position (4, 1)
[CheckerBoard_431494384582666] Created entity ID:7 ConfigID:101 at (4, 1) for player 90000076502
[CheckerBoard_431494384582666] Placed entity 8 at position (2, 5)
[CheckerBoard_431494384582666] Created entity ID:8 ConfigID:101 at (2, 5) for player 90000076502
[CheckerBoard_431494384582666] Placed entity 9 at position (1, 6)
[CheckerBoard_431494384582666] Created entity ID:9 ConfigID:102 at (1, 6) for player 90000076502
[CheckerBoard_431494384582666] Placed entity 10 at position (1, 2)
[CheckerBoard_431494384582666] Created entity ID:10 ConfigID:101 at (1, 2) for player 90000076502
[CheckerBoard_431494384582666] CheckTimes limit (4) reached for 1 hero types for player 90000076502
[CheckerBoard_431494384582666] Generated 5/5 heroes for player 90000076502: 5 placed, 0 in temporary slots
[CheckerBoard_431494384582666] Generated 5 heroes for player 90000076502 in My area
[AutoChessScene_431494384582666] Generated 5 heroes for player 90000076502: 5 placed on board, 0 in temporary slots
[AutoChessScene_431494384582666] Player status: Total=4, Active=4
[AutoChessScene_431494384582666] Player 10106021303: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431494384582666] Player 90000053744: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431494384582666] Player 90000042038: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431494384582666] Player 90000076502: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431494384582666] Sending RoundStart notifications to 4 active players...
[AutoChessScene_431494384582666] RoundStart board data: player:10106021303 heroes:5
[AutoChessScene_431494384582666] RoundStart board data: player:90000076502 heroes:5
[AutoChessScene_431494384582666] Sending RoundStart to Player 10106021303 on GameServer 10102
[AutoChessScene_431494384582666] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431494384582666] RoundStart board data: player:90000053744 heroes:5
[AutoChessScene_431494384582666] RoundStart board data: player:90000042038 heroes:5
[AutoChessScene_431494384582666] Sending RoundStart to Player 90000053744 on GameServer 10102
[AutoChessScene_431494384582666] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431494384582666] RoundStart board data: player:90000053744 heroes:5
[AutoChessScene_431494384582666] RoundStart board data: player:90000042038 heroes:5
[AutoChessScene_431494384582666] Sending RoundStart to Player 90000042038 on GameServer 10102
[AutoChessScene_431494384582666] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431494384582666] RoundStart board data: player:10106021303 heroes:5
[AutoChessScene_431494384582666] RoundStart board data: player:90000076502 heroes:5
[AutoChessScene_431494384582666] Sending RoundStart to Player 90000076502 on GameServer 10102
[AutoChessScene_431494384582666] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431494384582666] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 431494384582666 state to StateRoundStart
[AutoChessScene_431494384582666] State change sent to GameServer: StateNone -> StateRoundStart (R1)
[BattleStateManager_431494384582666] BattleStateChangedEvent published successfully
[BattleStateManager_431494384582666] ===== ROUND 1 INITIALIZATION COMPLETE =====
[AutoChessScene_431494384582666] Battle state machine started successfully for battle 431494384582666
[AutoChessScene_431494384582666] Current state after starting: StateRoundStart
[BattleService] Battle state update result: False, new state: StateRoundStart
[BattleStateManager_431494384582666] State: StateRoundStart -> StatePreparation (R1, 65000ms)
[AutoChessScene_431494384582666] Preparation phase started
[PlayerManager_431494384582666] Player 90000053744 ready status set to True
[PlayerManager_431494384582666] Player 90000042038 ready status set to True
[PlayerManager_431494384582666] Player 90000076502 ready status set to True
[AutoChessScene_431494384582666] Auto-ready 3 additional bots
[AutoChessScene_431494384582666] Free operation phase started
[BattleService] Updated battle 431494384582666 state to StatePreparation
[AutoChessScene_431494384582666] State change sent to GameServer: StateRoundStart -> StatePreparation (R1)
[BattleStateManager_431494384582666] BattleStateChangedEvent published successfully
[AutoChessScene_431494384582666] MergeHero operation: Player 10106021303, From GridID 5 → To GridID 11
[CheckerBoard_431494384582666] Removed entity ID:6 from (1, 5)
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_431494384582666] MergeHero operation: Player 10106021303, From GridID 6 → To GridID 19
[BattleInstance] 431494384582666_2 Processing 2 move operations before merge for player 10106021303
[CheckerBoard_431494384582666] Cannot move: target position (2, 5) is occupied by entity 8
[BattleInstance] 431494384582666_2 Move operation: 6 -> 11, success: False
[CheckerBoard_431494384582666] Cannot move: target position (1, 2) is occupied by entity 10
[BattleInstance] 431494384582666_2 Move operation: 6 -> 2, success: False
[CheckerBoard_431494384582666] Swapped entities between (1, 6) and (4, 1)
[AutoChessScene_431494384582666] MergeHero operation: Player 10106021303, From GridID 19 → To GridID 11
[CheckerBoard_431494384582666] Swapped entities between (4, 1) and (2, 5)
[PlayerManager_431494384582666] Player 10106021303 ready status set to True
[PlayerManager_431494384582666] All players are ready!
[AutoChessScene_431494384582666] All players are ready, transitioning to next state
[BattleStateManager_431494384582666] State: StatePreparation -> StateBattleStarting (R1, 1000ms)
[AutoChessScene_431494384582666] Applying battle start buffs for all players
[AutoChessScene_431494384582666] Camp info for player 10106021303: 5 heroes added
[AutoChessScene_431494384582666] Camp info for player 90000076502: 4 heroes added
[AutoChessScene_431494384582666] Created RoundBattleStart request for player 10106021303, Team order: [10106021303, 90000076502], total GridIDs used: 9
[AutoChessScene_431494384582666] Sent RoundBattleStart to Player 10106021303 vs Opponent 90000076502 with 2 teams
[AutoChessScene_431494384582666] Camp info for player 90000053744: 5 heroes added
[AutoChessScene_431494384582666] Camp info for player 90000042038: 5 heroes added
[AutoChessScene_431494384582666] Created RoundBattleStart request for player 90000053744, Team order: [90000053744, 90000042038], total GridIDs used: 10
[AutoChessScene_431494384582666] Sent RoundBattleStart to Player 90000053744 vs Opponent 90000042038 with 2 teams
[AutoChessScene_431494384582666] Camp info for player 90000053744: 5 heroes added
[AutoChessScene_431494384582666] Camp info for player 90000042038: 5 heroes added
[AutoChessScene_431494384582666] Created RoundBattleStart request for player 90000042038, Team order: [90000053744, 90000042038], total GridIDs used: 10
[AutoChessScene_431494384582666] Sent RoundBattleStart to Player 90000042038 vs Opponent 90000053744 with 2 teams
[AutoChessScene_431494384582666] Camp info for player 10106021303: 5 heroes added
[AutoChessScene_431494384582666] Camp info for player 90000076502: 4 heroes added
[AutoChessScene_431494384582666] Created RoundBattleStart request for player 90000076502, Team order: [10106021303, 90000076502], total GridIDs used: 9
[AutoChessScene_431494384582666] Sent RoundBattleStart to Player 90000076502 vs Opponent 10106021303 with 2 teams
[AutoChessScene_431494384582666] Sent RoundBattleStart notifications with seed: 554879861
[BattleService] Updated battle 431494384582666 state to StateBattleStarting
[AutoChessScene_431494384582666] State change sent to GameServer: StatePreparation -> StateBattleStarting (R1)
[BattleStateManager_431494384582666] BattleStateChangedEvent published successfully
[AutoChessScene_431494384582666] Player 10106021303 set ready status to True
[BattleStateManager_431494384582666] State: StateBattleStarting -> StateBattleInProgress (R1, 65000ms)
[AutoChessScene_431494384582666] Starting all battle instances
[BattleInstance] 431494384582666_1 battle started
[BattleInstance] 431494384582666_2 battle started
[BattleInstanceManager] Started all 2 battle instances
[AutoChessScene_431494384582666] Auto EndBattle for bot 90000053744 vs bot 90000042038, random result: bot 90000053744 wins = True
[AutoChessScene_431494384582666] Player 90000053744 sent EndBattleReq (win: True), instance: 431494384582666_1
[AutoChessScene_431494384582666] Waiting for opponent 90000042038 to send EndBattleReq for instance 431494384582666_1
[AutoChessScene_431494384582666] Player 90000042038 sent EndBattleReq (win: False), instance: 431494384582666_1
[BattleInstance] 431494384582666_1 battle finished, winner: 90000053744, loser: 90000042038
[AutoChessScene_431494384582666] Battle instance 431494384582666_1 completed: Winner 90000053744, Loser 90000042038
[AutoChessScene_431494384582666] Bot 90000076502 vs real player 10106021303, waiting for real player result
[AutoChessScene_431494384582666] Bot vs real player battles will be handled by system timeout (65s)
[BattleService] Updated battle 431494384582666 state to StateBattleInProgress
[AutoChessScene_431494384582666] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R1)
[BattleStateManager_431494384582666] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] LeaveBattle request received from player 10106021303
[BattleService] Player 10106021303 logout, cleaning up battle 431494384582666
[BattleService] Only one real player in battle 431494384582666, cleaning up entire battle
[BattleService] Starting cleanup for battle 431494384582666
[BattleService] Removed battle state for 431494384582666
[CheckerBoard_431494384582666] Cleared all entities
[BuffManager_431494384582666] Cleared all buffs
[AutoChessScene_431494384582666] Scene resources disposed
[SceneManager] Removed AutoChessScene 431494384582666 from thread management
[BattleService] Cleaned up scene for battle 431494384582666
[BattleService] Cleanup completed for battle 431494384582666
[BattleService] LeaveBattle completed successfully for player 10106021303
[NatsServer] Received publish message on subject: /30001/natsrpc.BattleService/LeaveBattle, no reply needed
