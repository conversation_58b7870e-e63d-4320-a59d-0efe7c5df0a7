[BattleService] Players expected to enter: [10106021303, 90000086053, 90000049108, 90000042465]
[BattleService] ===== IMPORTANT: Battle will NOT start until ALL players call EnterBattle =====
[BattleService] ===== GameServer should call EnterBattle for each player after scene loading =====
[BattleService] ===== BOT STRATEGY: Player 10106021303 is real player, others are bots =====
[BattleService] Auto-entered bot player 90000086053 immediately
[BattleService] Auto-entered bot player 90000049108 immediately
[BattleService] Auto-entered bot player 90000042465 immediately
[BattleService] Battle 431145536454671 bots auto-entered: 3/4 players ready
[BattleService] Status: 1 waiting (3/4 entered), 0 active
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10106021303 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10106021303 entered battle 431145536454671 (initial), current count: 4
[BattleService] All 4 players entered battle 431145536454671, starting battle state machine
[BattleService] Entered players: [90000086053, 90000049108, 90000042465, 10106021303]
[BattleService] Triggering AutoChessScene.StartBattleStateMachine() for battle 431145536454671
[AutoChessScene_431145536454671] StartBattleStateMachine() called for battle 431145536454671
[AutoChessScene_431145536454671] BattleStateManager is ready, starting first round...
[AutoChessScene_431145536454671] Current state before starting: StateNone
[BattleStateManager_431145536454671] ===== STARTING NEW ROUND 1 =====
[BattleStateManager_431145536454671] Round 1 has buff selection: False
[BattleStateManager_431145536454671] Publishing RoundStartedEvent for round 1
[AutoChessScene_431145536454671] Round 1 started
[BattleStateManager_431145536454671] Setting state to StateRoundStart for round 1
[BattleStateManager_431145536454671] State: StateNone -> StateRoundStart (R1, 1000ms)
[AutoChessScene_431145536454671] HandleRoundStart: 4 active players
[AutoChessScene_431145536454671] Valid player: 10106021303, Health: 3
[AutoChessScene_431145536454671] Valid player: 90000086053, Health: 3
[AutoChessScene_431145536454671] Valid player: 90000049108, Health: 3
[AutoChessScene_431145536454671] Valid player: 90000042465, Health: 3
[AutoChessScene_431145536454671] No instance found for player 10106021303 when saving board data
[AutoChessScene_431145536454671] No instance found for player 90000086053 when saving board data
[AutoChessScene_431145536454671] No instance found for player 90000049108 when saving board data
[AutoChessScene_431145536454671] No instance found for player 90000042465 when saving board data
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player 90000086053 vs Player 90000049108
[OpponentPairManager] Random pair: Player 90000042465 vs Player 10106021303
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_431145536454671] Created 4 opponent pairs
[PlayerManager_431145536454671] Set player opponents, count: 4
[BattleInstanceManager] Created instance 431145536454671_1 for active players 90000086053 vs 90000049108
[CheckerBoard_431145536454671] Cleared checkerboard
[CheckerBoard_431145536454671] Initialized
[BattleInstance] 431145536454671_1 created with players: 90000086053, 90000049108
[BattleInstanceManager] Created instance 431145536454671_2 for active players 90000042465 vs 10106021303
[CheckerBoard_431145536454671] Cleared checkerboard
[CheckerBoard_431145536454671] Initialized
[BattleInstance] 431145536454671_2 created with players: 90000042465, 10106021303
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_431145536454671] Cleaned orphaned entities for player 10106021303
[AutoChessScene_431145536454671] Cleaned orphaned entities for player 90000086053
[AutoChessScene_431145536454671] Cleaned orphaned entities for player 90000049108
[AutoChessScene_431145536454671] Cleaned orphaned entities for player 90000042465
[PlayerManager_431145536454671] Reset all players ready status
[AutoChessScene_431145536454671] Round started with 2 battle instances
[AutoChessScene_431145536454671] Generating 5 heroes for all players in round 1
[CheckerBoard_431145536454671] Placed entity 1 at position (6, 4)
[CheckerBoard_431145536454671] Created entity ID:1 ConfigID:101 at (6, 4) for player 10106021303
[CheckerBoard_431145536454671] Placed entity 2 at position (8, 1)
[CheckerBoard_431145536454671] Created entity ID:2 ConfigID:103 at (8, 1) for player 10106021303
[CheckerBoard_431145536454671] Placed entity 3 at position (6, 3)
[CheckerBoard_431145536454671] Created entity ID:3 ConfigID:101 at (6, 3) for player 10106021303
[CheckerBoard_431145536454671] Placed entity 4 at position (10, 4)
[CheckerBoard_431145536454671] Created entity ID:4 ConfigID:102 at (10, 4) for player 10106021303
[CheckerBoard_431145536454671] Placed entity 5 at position (6, 1)
[CheckerBoard_431145536454671] Created entity ID:5 ConfigID:103 at (6, 1) for player 10106021303
[CheckerBoard_431145536454671] Generated 5/5 heroes for player 10106021303: 5 placed, 0 in temporary slots
[CheckerBoard_431145536454671] Generated 5 heroes for player 10106021303 in Enemy area
[AutoChessScene_431145536454671] Generated 5 heroes for player 10106021303: 5 placed on board, 0 in temporary slots
[CheckerBoard_431145536454671] Placed entity 1 at position (3, 2)
[CheckerBoard_431145536454671] Created entity ID:1 ConfigID:102 at (3, 2) for player 90000086053
[CheckerBoard_431145536454671] Placed entity 2 at position (4, 6)
[CheckerBoard_431145536454671] Created entity ID:2 ConfigID:103 at (4, 6) for player 90000086053
[CheckerBoard_431145536454671] Placed entity 3 at position (1, 3)
[CheckerBoard_431145536454671] Created entity ID:3 ConfigID:101 at (1, 3) for player 90000086053
[CheckerBoard_431145536454671] Placed entity 4 at position (3, 1)
[CheckerBoard_431145536454671] Created entity ID:4 ConfigID:101 at (3, 1) for player 90000086053
[CheckerBoard_431145536454671] Placed entity 5 at position (5, 1)
[CheckerBoard_431145536454671] Created entity ID:5 ConfigID:101 at (5, 1) for player 90000086053
[CheckerBoard_431145536454671] Generated 5/5 heroes for player 90000086053: 5 placed, 0 in temporary slots
[CheckerBoard_431145536454671] Generated 5 heroes for player 90000086053 in My area
[AutoChessScene_431145536454671] Generated 5 heroes for player 90000086053: 5 placed on board, 0 in temporary slots
[CheckerBoard_431145536454671] Placed entity 6 at position (6, 1)
[CheckerBoard_431145536454671] Created entity ID:6 ConfigID:102 at (6, 1) for player 90000049108
[CheckerBoard_431145536454671] Placed entity 7 at position (10, 2)
[CheckerBoard_431145536454671] Created entity ID:7 ConfigID:103 at (10, 2) for player 90000049108
[CheckerBoard_431145536454671] Placed entity 8 at position (6, 3)
[CheckerBoard_431145536454671] Created entity ID:8 ConfigID:102 at (6, 3) for player 90000049108
[CheckerBoard_431145536454671] Placed entity 9 at position (10, 6)
[CheckerBoard_431145536454671] Created entity ID:9 ConfigID:101 at (10, 6) for player 90000049108
[CheckerBoard_431145536454671] Placed entity 10 at position (8, 2)
[CheckerBoard_431145536454671] Created entity ID:10 ConfigID:102 at (8, 2) for player 90000049108
[CheckerBoard_431145536454671] Generated 5/5 heroes for player 90000049108: 5 placed, 0 in temporary slots
[CheckerBoard_431145536454671] Generated 5 heroes for player 90000049108 in Enemy area
[AutoChessScene_431145536454671] Generated 5 heroes for player 90000049108: 5 placed on board, 0 in temporary slots
[CheckerBoard_431145536454671] Placed entity 6 at position (4, 4)
[CheckerBoard_431145536454671] Created entity ID:6 ConfigID:103 at (4, 4) for player 90000042465
[CheckerBoard_431145536454671] Placed entity 7 at position (2, 6)
[CheckerBoard_431145536454671] Created entity ID:7 ConfigID:103 at (2, 6) for player 90000042465
[CheckerBoard_431145536454671] Placed entity 8 at position (5, 2)
[CheckerBoard_431145536454671] Created entity ID:8 ConfigID:101 at (5, 2) for player 90000042465
[CheckerBoard_431145536454671] Placed entity 9 at position (2, 3)
[CheckerBoard_431145536454671] Created entity ID:9 ConfigID:102 at (2, 3) for player 90000042465
[CheckerBoard_431145536454671] Placed entity 10 at position (2, 5)
[CheckerBoard_431145536454671] Created entity ID:10 ConfigID:103 at (2, 5) for player 90000042465
[CheckerBoard_431145536454671] Generated 5/5 heroes for player 90000042465: 5 placed, 0 in temporary slots
[CheckerBoard_431145536454671] Generated 5 heroes for player 90000042465 in My area
[AutoChessScene_431145536454671] Generated 5 heroes for player 90000042465: 5 placed on board, 0 in temporary slots
[AutoChessScene_431145536454671] Player status: Total=4, Active=4
[AutoChessScene_431145536454671] Player 10106021303: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431145536454671] Player 90000086053: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431145536454671] Player 90000049108: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431145536454671] Player 90000042465: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431145536454671] Sending RoundStart notifications to 4 active players...
[AutoChessScene_431145536454671] RoundStart board data: player:90000042465 heroes:5
[AutoChessScene_431145536454671] RoundStart board data: player:10106021303 heroes:5
[AutoChessScene_431145536454671] Sending RoundStart to Player 10106021303 on GameServer 10102
[AutoChessScene_431145536454671] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431145536454671] RoundStart board data: player:90000086053 heroes:5
[AutoChessScene_431145536454671] RoundStart board data: player:90000049108 heroes:5
[AutoChessScene_431145536454671] Sending RoundStart to Player 90000086053 on GameServer 10102
[AutoChessScene_431145536454671] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431145536454671] RoundStart board data: player:90000086053 heroes:5
[AutoChessScene_431145536454671] RoundStart board data: player:90000049108 heroes:5
[AutoChessScene_431145536454671] Sending RoundStart to Player 90000049108 on GameServer 10102
[AutoChessScene_431145536454671] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431145536454671] RoundStart board data: player:90000042465 heroes:5
[AutoChessScene_431145536454671] RoundStart board data: player:10106021303 heroes:5
[AutoChessScene_431145536454671] Sending RoundStart to Player 90000042465 on GameServer 10102
[AutoChessScene_431145536454671] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431145536454671] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 431145536454671 state to StateRoundStart
[AutoChessScene_431145536454671] State change sent to GameServer: StateNone -> StateRoundStart (R1)
[BattleStateManager_431145536454671] BattleStateChangedEvent published successfully
[BattleStateManager_431145536454671] ===== ROUND 1 INITIALIZATION COMPLETE =====
[AutoChessScene_431145536454671] Battle state machine started successfully for battle 431145536454671
[AutoChessScene_431145536454671] Current state after starting: StateRoundStart
[BattleService] Battle state update result: False, new state: StateRoundStart
[BattleStateManager_431145536454671] State: StateRoundStart -> StatePreparation (R1, 65000ms)
[AutoChessScene_431145536454671] Preparation phase started
[PlayerManager_431145536454671] Player 90000086053 ready status set to True
[PlayerManager_431145536454671] Player 90000049108 ready status set to True
[PlayerManager_431145536454671] Player 90000042465 ready status set to True
[AutoChessScene_431145536454671] Auto-ready 3 additional bots
[AutoChessScene_431145536454671] Free operation phase started
[BattleService] Updated battle 431145536454671 state to StatePreparation
[AutoChessScene_431145536454671] State change sent to GameServer: StateRoundStart -> StatePreparation (R1)
[BattleStateManager_431145536454671] BattleStateChangedEvent published successfully
[AutoChessScene_431145536454671] MergeHero operation: Player 10106021303, From GridID 33 → To GridID 34
[CheckerBoard_431145536454671] Removed entity ID:3 from (6, 3)
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_431145536454671] MergeHero operation: Player 10106021303, From GridID 31 → To GridID 43
[CheckerBoard_431145536454671] Removed entity ID:5 from (6, 1)
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[PlayerManager_431145536454671] Player 10106021303 ready status set to True
[PlayerManager_431145536454671] All players are ready!
[AutoChessScene_431145536454671] All players are ready, transitioning to next state
[BattleStateManager_431145536454671] State: StatePreparation -> StateBattleStarting (R1, 1000ms)
[AutoChessScene_431145536454671] Applying battle start buffs for all players
[AutoChessScene_431145536454671] Camp info for player 90000042465: 5 heroes added
[AutoChessScene_431145536454671] Camp info for player 10106021303: 3 heroes added
[AutoChessScene_431145536454671] Created RoundBattleStart request for player 10106021303, Team order: [90000042465, 10106021303], total GridIDs used: 8
[AutoChessScene_431145536454671] Sent RoundBattleStart to Player 10106021303 vs Opponent 90000042465 with 2 teams
[AutoChessScene_431145536454671] Camp info for player 90000086053: 5 heroes added
[AutoChessScene_431145536454671] Camp info for player 90000049108: 5 heroes added
[AutoChessScene_431145536454671] Created RoundBattleStart request for player 90000086053, Team order: [90000086053, 90000049108], total GridIDs used: 10
[AutoChessScene_431145536454671] Sent RoundBattleStart to Player 90000086053 vs Opponent 90000049108 with 2 teams
[AutoChessScene_431145536454671] Camp info for player 90000086053: 5 heroes added
[AutoChessScene_431145536454671] Camp info for player 90000049108: 5 heroes added
[AutoChessScene_431145536454671] Created RoundBattleStart request for player 90000049108, Team order: [90000086053, 90000049108], total GridIDs used: 10
[AutoChessScene_431145536454671] Sent RoundBattleStart to Player 90000049108 vs Opponent 90000086053 with 2 teams
[AutoChessScene_431145536454671] Camp info for player 90000042465: 5 heroes added
[AutoChessScene_431145536454671] Camp info for player 10106021303: 3 heroes added
[AutoChessScene_431145536454671] Created RoundBattleStart request for player 90000042465, Team order: [90000042465, 10106021303], total GridIDs used: 8
[AutoChessScene_431145536454671] Sent RoundBattleStart to Player 90000042465 vs Opponent 10106021303 with 2 teams
[AutoChessScene_431145536454671] Sent RoundBattleStart notifications with seed: 2022739331
[BattleService] Updated battle 431145536454671 state to StateBattleStarting
[AutoChessScene_431145536454671] State change sent to GameServer: StatePreparation -> StateBattleStarting (R1)
[BattleStateManager_431145536454671] BattleStateChangedEvent published successfully
[AutoChessScene_431145536454671] Player 10106021303 set ready status to True
[BattleStateManager_431145536454671] State: StateBattleStarting -> StateBattleInProgress (R1, 65000ms)
[AutoChessScene_431145536454671] Starting all battle instances
[BattleInstance] 431145536454671_1 battle started
[BattleInstance] 431145536454671_2 battle started
[BattleInstanceManager] Started all 2 battle instances
[AutoChessScene_431145536454671] Auto EndBattle for bot 90000086053 vs bot 90000049108, random result: bot 90000086053 wins = True
[AutoChessScene_431145536454671] Player 90000086053 sent EndBattleReq (win: True), instance: 431145536454671_1
[AutoChessScene_431145536454671] Waiting for opponent 90000049108 to send EndBattleReq for instance 431145536454671_1
[AutoChessScene_431145536454671] Player 90000049108 sent EndBattleReq (win: False), instance: 431145536454671_1
[BattleInstance] 431145536454671_1 battle finished, winner: 90000086053, loser: 90000049108
[AutoChessScene_431145536454671] Battle instance 431145536454671_1 completed: Winner 90000086053, Loser 90000049108
[AutoChessScene_431145536454671] Bot 90000042465 vs real player 10106021303, waiting for real player result
[AutoChessScene_431145536454671] Bot vs real player battles will be handled by system timeout (65s)
[BattleService] Updated battle 431145536454671 state to StateBattleInProgress
[AutoChessScene_431145536454671] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R1)
[BattleStateManager_431145536454671] BattleStateChangedEvent published successfully
[BattleService] EndBattle uid: 10106021303, win: True
[AutoChessScene_431145536454671] Player 10106021303 sent EndBattleReq (win: True), instance: 431145536454671_2
[AutoChessScene_431145536454671] Auto EndBattle for bot 90000042465 vs real player 10106021303, bot result: False
[BattleInstance] 431145536454671_2 battle finished, winner: 10106021303, loser: 90000042465
[AutoChessScene_431145536454671] Battle instance 431145536454671_2 completed: Winner 10106021303, Loser 90000042465
[AutoChessScene_431145536454671] All battle instances finished, proceeding to settlement
[BattleStateManager_431145536454671] State: StateBattleInProgress -> StateRoundSettlement (R1, 5000ms)
[AutoChessScene_431145536454671] Processing battle results
[PlayerManager_431145536454671] Player 90000049108 health reduced by 1, current health: 2
[AutoChessScene_431145536454671] Player 90000049108 lost 1 health, winner: 90000086053
[AutoChessScene_431145536454671] Sent RoundBattleEnd notification (isEnd=false) - Winner: 90000086053, Loser: 90000049108
[PlayerManager_431145536454671] Player 90000042465 health reduced by 1, current health: 2
[AutoChessScene_431145536454671] Player 90000042465 lost 1 health, winner: 10106021303
[AutoChessScene_431145536454671] Sent RoundBattleEnd notification (isEnd=false) - Winner: 10106021303, Loser: 90000042465
[AutoChessScene_431145536454671] Checking players elimination
[AutoChessScene_431145536454671] Active players remaining: 4
[AutoChessScene_431145536454671] Waiting for all players to confirm round settlement before starting new round
[AutoChessScene_431145536454671] Auto-confirming round settlement for bot 90000086053
[AutoChessScene_431145536454671] Auto-confirming round settlement for bot 90000049108
[AutoChessScene_431145536454671] Auto-confirming round settlement for bot 90000042465
[BattleService] Updated battle 431145536454671 state to StateRoundSettlement
[AutoChessScene_431145536454671] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R1)
[BattleStateManager_431145536454671] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10106021303 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10106021303 confirmed round settlement, current state: StateRoundSettlement
[AutoChessScene_431145536454671] Player 10106021303 confirmed round settlement, count: 4
[AutoChessScene_431145536454671] Real player 10106021303 confirmed, auto-confirming all bots
[AutoChessScene_431145536454671] All players confirmed round settlement, starting new round
[PlayerManager_431145536454671] Player 10106021303 ready status set to False
[PlayerManager_431145536454671] Player 90000086053 ready status set to False
[PlayerManager_431145536454671] Player 90000049108 ready status set to False
[PlayerManager_431145536454671] Player 90000042465 ready status set to False
[BattleStateManager_431145536454671] ===== STARTING NEW ROUND 2 =====
[BattleStateManager_431145536454671] Round 2 has buff selection: True
[BattleStateManager_431145536454671] Publishing RoundStartedEvent for round 2
[AutoChessScene_431145536454671] Round 2 started
[BattleStateManager_431145536454671] Setting state to StateRoundStart for round 2
[BattleStateManager_431145536454671] State: StateRoundSettlement -> StateRoundStart (R2, 1000ms)
[AutoChessScene_431145536454671] HandleRoundStart: 4 active players
[AutoChessScene_431145536454671] Valid player: 10106021303, Health: 3
[AutoChessScene_431145536454671] Valid player: 90000086053, Health: 3
[AutoChessScene_431145536454671] Valid player: 90000049108, Health: 2
[AutoChessScene_431145536454671] Valid player: 90000042465, Health: 2
[AutoChessScene_431145536454671] Player 10106021303 has 3 entities to save
[PlayerManager_431145536454671] Saved board data: player:10106021303 entities:3
[PlayerManager_431145536454671] Saved prev round data: player:10106021303 entities:3
[AutoChessScene_431145536454671] Player 90000086053 has 5 entities to save
[PlayerManager_431145536454671] Saved board data: player:90000086053 entities:5
[PlayerManager_431145536454671] Saved prev round data: player:90000086053 entities:5
[AutoChessScene_431145536454671] Player 90000049108 has 5 entities to save
[PlayerManager_431145536454671] Saved board data: player:90000049108 entities:5
[PlayerManager_431145536454671] Saved prev round data: player:90000049108 entities:5
[AutoChessScene_431145536454671] Player 90000042465 has 5 entities to save
[PlayerManager_431145536454671] Saved board data: player:90000042465 entities:5
[PlayerManager_431145536454671] Saved prev round data: player:90000042465 entities:5
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player 90000086053 vs Player 90000042465
[OpponentPairManager] Random pair: Player 10106021303 vs Player 90000049108
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_431145536454671] Created 4 opponent pairs
[PlayerManager_431145536454671] Set player opponents, count: 4
[BattleInstanceManager] Created instance 431145536454671_1 for active players 90000086053 vs 90000042465
[CheckerBoard_431145536454671] Cleared checkerboard
[CheckerBoard_431145536454671] Initialized
[BattleInstance] 431145536454671_1 created with players: 90000086053, 90000042465
[BattleInstanceManager] Created instance 431145536454671_2 for active players 10106021303 vs 90000049108
[CheckerBoard_431145536454671] Cleared checkerboard
[CheckerBoard_431145536454671] Initialized
[BattleInstance] 431145536454671_2 created with players: 10106021303, 90000049108
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_431145536454671] Restoring player 10106021303 to My area (rows 1-5)
[CheckerBoard_431145536454671] Placed entity 1 at position (1, 4)
[CheckerBoard_431145536454671] Created entity ID:1 ConfigID:101 at (1, 4) for player 10106021303
[AutoChessScene_431145536454671] Restored entity 1: (6,4)->(1,4), GridID:34->4
[CheckerBoard_431145536454671] Placed entity 2 at position (3, 1)
[CheckerBoard_431145536454671] Created entity ID:2 ConfigID:103 at (3, 1) for player 10106021303
[AutoChessScene_431145536454671] Restored entity 2: (8,1)->(3,1), GridID:43->13
[CheckerBoard_431145536454671] Placed entity 3 at position (5, 4)
[CheckerBoard_431145536454671] Created entity ID:3 ConfigID:102 at (5, 4) for player 10106021303
[AutoChessScene_431145536454671] Restored entity 3: (10,4)->(5,4), GridID:58->28
[AutoChessScene_431145536454671] Restored board: player:10106021303 entities:3
[AutoChessScene_431145536454671] Restoring player 90000086053 to My area (rows 1-5)
[CheckerBoard_431145536454671] Placed entity 1 at position (3, 2)
[CheckerBoard_431145536454671] Created entity ID:1 ConfigID:102 at (3, 2) for player 90000086053
[AutoChessScene_431145536454671] Restored entity 1: (3,2)->(3,2), GridID:14->14
[CheckerBoard_431145536454671] Placed entity 2 at position (4, 6)
[CheckerBoard_431145536454671] Created entity ID:2 ConfigID:103 at (4, 6) for player 90000086053
[AutoChessScene_431145536454671] Restored entity 2: (4,6)->(4,6), GridID:24->24
[CheckerBoard_431145536454671] Placed entity 3 at position (1, 3)
[CheckerBoard_431145536454671] Created entity ID:3 ConfigID:101 at (1, 3) for player 90000086053
[AutoChessScene_431145536454671] Restored entity 3: (1,3)->(1,3), GridID:3->3
[CheckerBoard_431145536454671] Placed entity 4 at position (3, 1)
[CheckerBoard_431145536454671] Created entity ID:4 ConfigID:101 at (3, 1) for player 90000086053
[AutoChessScene_431145536454671] Restored entity 4: (3,1)->(3,1), GridID:13->13
[CheckerBoard_431145536454671] Placed entity 5 at position (5, 1)
[CheckerBoard_431145536454671] Created entity ID:5 ConfigID:101 at (5, 1) for player 90000086053
[AutoChessScene_431145536454671] Restored entity 5: (5,1)->(5,1), GridID:25->25
[AutoChessScene_431145536454671] Restored board: player:90000086053 entities:5
[AutoChessScene_431145536454671] Restoring player 90000049108 to Enemy area (rows 6-10)
[CheckerBoard_431145536454671] Placed entity 4 at position (6, 1)
[CheckerBoard_431145536454671] Created entity ID:4 ConfigID:102 at (6, 1) for player 90000049108
[AutoChessScene_431145536454671] Restored entity 4: (6,1)->(6,1), GridID:31->31
[CheckerBoard_431145536454671] Placed entity 5 at position (10, 2)
[CheckerBoard_431145536454671] Created entity ID:5 ConfigID:103 at (10, 2) for player 90000049108
[AutoChessScene_431145536454671] Restored entity 5: (10,2)->(10,2), GridID:56->56
[CheckerBoard_431145536454671] Placed entity 6 at position (6, 3)
[CheckerBoard_431145536454671] Created entity ID:6 ConfigID:102 at (6, 3) for player 90000049108
[AutoChessScene_431145536454671] Restored entity 6: (6,3)->(6,3), GridID:33->33
[CheckerBoard_431145536454671] Placed entity 7 at position (10, 6)
[CheckerBoard_431145536454671] Created entity ID:7 ConfigID:101 at (10, 6) for player 90000049108
[AutoChessScene_431145536454671] Restored entity 7: (10,6)->(10,6), GridID:60->60
[CheckerBoard_431145536454671] Placed entity 8 at position (8, 2)
[CheckerBoard_431145536454671] Created entity ID:8 ConfigID:102 at (8, 2) for player 90000049108
[AutoChessScene_431145536454671] Restored entity 8: (8,2)->(8,2), GridID:44->44
[AutoChessScene_431145536454671] Restored board: player:90000049108 entities:5
[AutoChessScene_431145536454671] Restoring player 90000042465 to Enemy area (rows 6-10)
[CheckerBoard_431145536454671] Placed entity 6 at position (9, 4)
[CheckerBoard_431145536454671] Created entity ID:6 ConfigID:103 at (9, 4) for player 90000042465
[AutoChessScene_431145536454671] Restored entity 6: (4,4)->(9,4), GridID:22->52
[CheckerBoard_431145536454671] Placed entity 7 at position (7, 6)
[CheckerBoard_431145536454671] Created entity ID:7 ConfigID:103 at (7, 6) for player 90000042465
[AutoChessScene_431145536454671] Restored entity 7: (2,6)->(7,6), GridID:12->42
[CheckerBoard_431145536454671] Placed entity 8 at position (10, 2)
[CheckerBoard_431145536454671] Created entity ID:8 ConfigID:101 at (10, 2) for player 90000042465
[AutoChessScene_431145536454671] Restored entity 8: (5,2)->(10,2), GridID:26->56
[CheckerBoard_431145536454671] Placed entity 9 at position (7, 3)
[CheckerBoard_431145536454671] Created entity ID:9 ConfigID:102 at (7, 3) for player 90000042465
[AutoChessScene_431145536454671] Restored entity 9: (2,3)->(7,3), GridID:9->39
[CheckerBoard_431145536454671] Placed entity 10 at position (7, 5)
[CheckerBoard_431145536454671] Created entity ID:10 ConfigID:103 at (7, 5) for player 90000042465
[AutoChessScene_431145536454671] Restored entity 10: (2,5)->(7,5), GridID:11->41
[AutoChessScene_431145536454671] Restored board: player:90000042465 entities:5
[AutoChessScene_431145536454671] Cleaned orphaned entities for player 10106021303
[AutoChessScene_431145536454671] Cleaned orphaned entities for player 90000086053
[AutoChessScene_431145536454671] Cleaned orphaned entities for player 90000049108
[AutoChessScene_431145536454671] Cleaned orphaned entities for player 90000042465
[PlayerManager_431145536454671] Reset all players ready status
[AutoChessScene_431145536454671] Round started with 2 battle instances
[AutoChessScene_431145536454671] Generating buff options for all players
[BuffManager_431145536454671] Generated 3 buff options for player 10106021303: [101, 109, 103]
[AutoChessScene_431145536454671] Generated 3 buff options for player 10106021303: [101, 109, 103]
[BuffManager_431145536454671] Generated 3 buff options for player 90000086053: [105, 103, 107]
[AutoChessScene_431145536454671] Generated 3 buff options for player 90000086053: [105, 103, 107]
[BuffManager_431145536454671] Generated 3 buff options for player 90000049108: [101, 103, 107]
[AutoChessScene_431145536454671] Generated 3 buff options for player 90000049108: [101, 103, 107]
[BuffManager_431145536454671] Generated 3 buff options for player 90000042465: [106, 103, 107]
[AutoChessScene_431145536454671] Generated 3 buff options for player 90000042465: [106, 103, 107]
[AutoChessScene_431145536454671] Player status: Total=4, Active=4
[AutoChessScene_431145536454671] Player 10106021303: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431145536454671] Player 90000086053: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431145536454671] Player 90000049108: Eliminated=False, Health=2, HasInstance=True
[AutoChessScene_431145536454671] Player 90000042465: Eliminated=False, Health=2, HasInstance=True
[AutoChessScene_431145536454671] Sending RoundStart notifications to 4 active players...
[AutoChessScene_431145536454671] RoundStart: Player 10106021303 buff options: [101, 109, 103]
[AutoChessScene_431145536454671] RoundStart board data: player:10106021303 heroes:3
[AutoChessScene_431145536454671] RoundStart board data: player:90000049108 heroes:5
[AutoChessScene_431145536454671] Sending RoundStart to Player 10106021303 on GameServer 10102
[AutoChessScene_431145536454671] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_431145536454671] RoundStart: Player 90000086053 buff options: [105, 103, 107]
[AutoChessScene_431145536454671] RoundStart board data: player:90000086053 heroes:5
[AutoChessScene_431145536454671] RoundStart board data: player:90000042465 heroes:5
[AutoChessScene_431145536454671] Sending RoundStart to Player 90000086053 on GameServer 10102
[AutoChessScene_431145536454671] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_431145536454671] RoundStart: Player 90000049108 buff options: [101, 103, 107]
[AutoChessScene_431145536454671] RoundStart board data: player:10106021303 heroes:3
[AutoChessScene_431145536454671] RoundStart board data: player:90000049108 heroes:5
[AutoChessScene_431145536454671] Sending RoundStart to Player 90000049108 on GameServer 10102
[AutoChessScene_431145536454671] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_431145536454671] RoundStart: Player 90000042465 buff options: [106, 103, 107]
[AutoChessScene_431145536454671] RoundStart board data: player:90000086053 heroes:5
[AutoChessScene_431145536454671] RoundStart board data: player:90000042465 heroes:5
[AutoChessScene_431145536454671] Sending RoundStart to Player 90000042465 on GameServer 10102
[AutoChessScene_431145536454671] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_431145536454671] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 431145536454671 state to StateRoundStart
[AutoChessScene_431145536454671] State change sent to GameServer: StateRoundSettlement -> StateRoundStart (R2)
[BattleStateManager_431145536454671] BattleStateChangedEvent published successfully
[BattleStateManager_431145536454671] ===== ROUND 2 INITIALIZATION COMPLETE =====
[BattleStateManager_431145536454671] Buff selection timer started: 25000ms
[BattleStateManager_431145536454671] State: StateRoundStart -> StatePreparation (R2, 65000ms)
[AutoChessScene_431145536454671] Preparation phase started
[BuffManager_431145536454671] Added buff 105 (Buff_105) to player 90000086053
[AutoChessScene_431145536454671] Auto-selected buff 105 for bot player 90000086053
[CheckerBoard_431145536454671] Placed entity 11 at position (2, 2)
[CheckerBoard_431145536454671] Created entity ID:11 ConfigID:103 at (2, 2) for player 90000086053
[CheckerBoard_431145536454671] Placed entity 12 at position (5, 6)
[CheckerBoard_431145536454671] Created entity ID:12 ConfigID:101 at (5, 6) for player 90000086053
[CheckerBoard_431145536454671] Placed entity 13 at position (1, 4)
[CheckerBoard_431145536454671] Created entity ID:13 ConfigID:101 at (1, 4) for player 90000086053
[CheckerBoard_431145536454671] Placed entity 14 at position (4, 3)
[CheckerBoard_431145536454671] Created entity ID:14 ConfigID:102 at (4, 3) for player 90000086053
[CheckerBoard_431145536454671] Placed entity 15 at position (1, 6)
[CheckerBoard_431145536454671] Created entity ID:15 ConfigID:103 at (1, 6) for player 90000086053
[CheckerBoard_431145536454671] Generated 5/5 heroes for player 90000086053: 5 placed, 0 in temporary slots
[CheckerBoard_431145536454671] Generated 5 heroes for player 90000086053 in My area
[AutoChessScene_431145536454671] Generated 5 heroes for player 90000086053: 5 placed on board, 0 in temporary slots
[AutoChessScene_431145536454671] Generated 5 new heroes for bot player 90000086053 after buff selection
[BuffManager_431145536454671] Added buff 101 (Buff_101) to player 90000049108
[AutoChessScene_431145536454671] Auto-selected buff 101 for bot player 90000049108
[CheckerBoard_431145536454671] Placed entity 9 at position (6, 5)
[CheckerBoard_431145536454671] Created entity ID:9 ConfigID:103 at (6, 5) for player 90000049108
[CheckerBoard_431145536454671] Placed entity 10 at position (9, 5)
[CheckerBoard_431145536454671] Created entity ID:10 ConfigID:103 at (9, 5) for player 90000049108
[CheckerBoard_431145536454671] Placed entity 11 at position (10, 5)
[CheckerBoard_431145536454671] Created entity ID:11 ConfigID:103 at (10, 5) for player 90000049108
[CheckerBoard_431145536454671] Placed entity 12 at position (6, 6)
[CheckerBoard_431145536454671] Created entity ID:12 ConfigID:103 at (6, 6) for player 90000049108
[CheckerBoard_431145536454671] Placed entity 13 at position (9, 6)
[CheckerBoard_431145536454671] Created entity ID:13 ConfigID:101 at (9, 6) for player 90000049108
[CheckerBoard_431145536454671] CheckTimes limit (4) reached for 1 hero types for player 90000049108
[CheckerBoard_431145536454671] Generated 5/5 heroes for player 90000049108: 5 placed, 0 in temporary slots
[CheckerBoard_431145536454671] Generated 5 heroes for player 90000049108 in Enemy area
[AutoChessScene_431145536454671] Generated 5 heroes for player 90000049108: 5 placed on board, 0 in temporary slots
[AutoChessScene_431145536454671] Generated 5 new heroes for bot player 90000049108 after buff selection
[BuffManager_431145536454671] Added buff 106 (Buff_106) to player 90000042465
[AutoChessScene_431145536454671] Auto-selected buff 106 for bot player 90000042465
[CheckerBoard_431145536454671] Placed entity 16 at position (6, 6)
[CheckerBoard_431145536454671] Created entity ID:16 ConfigID:103 at (6, 6) for player 90000042465
[CheckerBoard_431145536454671] Placed entity 17 at position (9, 2)
[CheckerBoard_431145536454671] Created entity ID:17 ConfigID:102 at (9, 2) for player 90000042465
[CheckerBoard_431145536454671] Placed entity 18 at position (10, 5)
[CheckerBoard_431145536454671] Created entity ID:18 ConfigID:101 at (10, 5) for player 90000042465
[CheckerBoard_431145536454671] Placed entity 19 at position (10, 1)
[CheckerBoard_431145536454671] Created entity ID:19 ConfigID:101 at (10, 1) for player 90000042465
[CheckerBoard_431145536454671] Placed entity 20 at position (9, 6)
[CheckerBoard_431145536454671] Created entity ID:20 ConfigID:102 at (9, 6) for player 90000042465
[CheckerBoard_431145536454671] Generated 5/5 heroes for player 90000042465: 5 placed, 0 in temporary slots
[CheckerBoard_431145536454671] Generated 5 heroes for player 90000042465 in Enemy area
[AutoChessScene_431145536454671] Generated 5 heroes for player 90000042465: 5 placed on board, 0 in temporary slots
[AutoChessScene_431145536454671] Generated 5 new heroes for bot player 90000042465 after buff selection
[PlayerManager_431145536454671] Player 90000086053 ready status set to True
[PlayerManager_431145536454671] Player 90000049108 ready status set to True
[PlayerManager_431145536454671] Player 90000042465 ready status set to True
[AutoChessScene_431145536454671] Auto-ready 3 additional bots
[AutoChessScene_431145536454671] Free operation phase started
[BattleService] Updated battle 431145536454671 state to StatePreparation
[AutoChessScene_431145536454671] State change sent to GameServer: StateRoundStart -> StatePreparation (R2)
[BattleStateManager_431145536454671] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED SelectBuffer RPC =====
[BattleService] Player 10106021303 is selecting buff 103
[BuffManager_431145536454671] Added buff 103 (Buff_103) to player 10106021303
[CheckerBoard_431145536454671] Placed entity 14 at position (5, 6)
[CheckerBoard_431145536454671] Created entity ID:14 ConfigID:101 at (5, 6) for player 10106021303
[CheckerBoard_431145536454671] Placed entity 15 at position (2, 1)
[CheckerBoard_431145536454671] Created entity ID:15 ConfigID:103 at (2, 1) for player 10106021303
[CheckerBoard_431145536454671] Placed entity 16 at position (4, 1)
[CheckerBoard_431145536454671] Created entity ID:16 ConfigID:103 at (4, 1) for player 10106021303
[CheckerBoard_431145536454671] Placed entity 17 at position (3, 6)
[CheckerBoard_431145536454671] Created entity ID:17 ConfigID:103 at (3, 6) for player 10106021303
[CheckerBoard_431145536454671] Placed entity 18 at position (5, 2)
[CheckerBoard_431145536454671] Created entity ID:18 ConfigID:102 at (5, 2) for player 10106021303
[CheckerBoard_431145536454671] Generated 5/5 heroes for player 10106021303: 5 placed, 0 in temporary slots
[CheckerBoard_431145536454671] Generated 5 heroes for player 10106021303 in My area
[AutoChessScene_431145536454671] Generated 5 heroes for player 10106021303: 5 placed on board, 0 in temporary slots
[AutoChessScene_431145536454671] Player 10106021303 selected buff 103, generated 5 new heroes
[BattleService] SelectBuffer response: Code=0, NewHeroes=5
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_431145536454671] Buff selection timeout occurred - forcing buff selection for all unselected players
[BattleStateManager_431145536454671] Buff selection timeout triggered
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] LeaveBattle request received from player 10106021303
[BattleService] Player 10106021303 logout, cleaning up battle 431145536454671
[BattleService] Only one real player in battle 431145536454671, cleaning up entire battle
[BattleService] Starting cleanup for battle 431145536454671
[BattleService] Removed battle state for 431145536454671
[CheckerBoard_431145536454671] Cleared all entities
[BuffManager_431145536454671] Cleared all buffs
[AutoChessScene_431145536454671] Scene resources disposed
[SceneManager] Removed AutoChessScene 431145536454671 from thread management
[BattleService] Cleaned up scene for battle 431145536454671
[BattleService] Cleanup completed for battle 431145536454671
[BattleService] LeaveBattle completed successfully for player 10106021303
[NatsServer] Received publish message on subject: /30001/natsrpc.BattleService/LeaveBattle, no reply needed
