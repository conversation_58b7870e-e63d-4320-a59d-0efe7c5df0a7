package nats_service

import (
	"context"
	"fmt"
	"liteframe/internal/common/natsrpc"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/table"
	"liteframe/internal/game-logic/matchserver/global"
	"liteframe/pkg/listmap"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
	"math/rand"
	"time"
)

// 自动象棋匹配类型常量
const (
	AutoChessMatchTypePVE        = 1 // PVE with configured bots
	AutoChessMatchTypePVPAsync   = 2 // PVP asynchronous (real player data with potential delay, AI operations)
	AutoChessMatchTypePVPRealtime = 3 // PVP real-time player matching
	AutoChessMatchTypePVPComposite = 4 // Composite type (Type 3 with timeout fallback to Type 2)
)

// 自动象棋玩家信息
type AutoChessPlayerInfo struct {
	battleInfo    *public.PBBattlePlayerInfo
	teamInfo      *public.PBBattleTeamInfo
	beginTime     int64  // 开始匹配时间
	serverGroup   string // 服务器组
	matchType     int32  // 匹配类型
	playerCount   int32  // 期望玩家数量
}

// 战斗房间信息 (无锁设计，通过channel通信)
type AutoChessBattleRoom struct {
	roomId       string                     // 房间ID
	creatorUid   uint64                     // 创建者UID
	roomScore    int32                      // 房间分数(创建者奖杯数)
	serverGroup  string                     // 服务器组
	matchType    int32                      // 匹配类型
	playerCount  int32                      // 期望玩家数量
	currentCount int32                      // 当前玩家数量
	players      []*AutoChessPlayerInfo     // 房间内玩家
	createTime   int64                      // 创建时间
	state        int32                      // 房间状态: 1=匹配中, 2=已满员, 3=已开始
}

// 自动象棋匹配器
type AutoChessMatch struct {
	// 匹配队列 - 按服务器组分组
	matchQueues map[string]*listmap.ListMap // key: serverGroup

	// 战斗房间管理 - 按服务器组分组
	battleRooms map[string]map[string]*AutoChessBattleRoom // key: serverGroup -> roomId

	// 通道
	addCh    chan *AutoChessPlayerInfo
	cancelCh chan uint64
	closeCh  chan struct{}
}

// 确保AutoChessMatch实现IMatch接口
var _ IMatch = (*AutoChessMatch)(nil)

func newAutoChessMatch() *AutoChessMatch {
	return &AutoChessMatch{
		matchQueues: make(map[string]*listmap.ListMap),
		battleRooms: make(map[string]map[string]*AutoChessBattleRoom),
		addCh:       make(chan *AutoChessPlayerInfo, channelSize),
		cancelCh:    make(chan uint64, channelSize),
		closeCh:     make(chan struct{}),
	}
}

func (m *AutoChessMatch) Start() {
	util.Go(m.Run)
	log.Info("AutoChess match started")
}

func (m *AutoChessMatch) Stop() {
	close(m.closeCh)
	log.Info("AutoChess match stopped")
}

func (m *AutoChessMatch) Match(req *natsrpc.MatchRequest) {
	// 解析玩家匹配类型和配置
	playerInfo := &AutoChessPlayerInfo{
		battleInfo:  req.Player,
		teamInfo:    req.Team,
		beginTime:   time.Now().Unix(),
		serverGroup: m.getServerGroup(req.Player.ServerId),
		matchType:   m.getMatchType(req.Player.Throphy),
		playerCount: m.getPlayerCount(), // 从配置读取，默认4
	}
	
	m.addCh <- playerInfo
	log.Info("AutoChess match request received", 
		log.Kv("uid", req.Player.Uid),
		log.Kv("serverGroup", playerInfo.serverGroup),
		log.Kv("matchType", playerInfo.matchType))
}

func (m *AutoChessMatch) CancelMatch(req *natsrpc.CancelMatchRequest) {
	m.cancelCh <- req.Uid
	log.Info("AutoChess match cancel request", log.Kv("uid", req.Uid))
}

func (m *AutoChessMatch) Run() {
	ticker := time.NewTicker(time.Millisecond * 500) // 500ms匹配间隔
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			m.processMatching()
		case player := <-m.addCh:
			m.handlePlayerJoin(player)
		case uid := <-m.cancelCh:
			m.handlePlayerCancel(uid)
		case <-m.closeCh:
			log.Info("AutoChess match manager closing")
			return
		}
	}
}

// 处理玩家加入匹配
func (m *AutoChessMatch) handlePlayerJoin(player *AutoChessPlayerInfo) {
	// 1. 尝试加入现有房间
	if room := m.findSuitableRoom(player); room != nil {
		if m.joinRoom(room, player) {
			log.Info("Player joined existing room", 
				log.Kv("uid", player.battleInfo.Uid),
				log.Kv("roomId", room.roomId))
			
			// 检查房间是否满员
			if room.currentCount >= room.playerCount {
				m.startBattle(room)
			}
			return
		}
	}
	
	// 2. 创建新房间
	room := m.createRoom(player)
	log.Info("Player created new room", 
		log.Kv("uid", player.battleInfo.Uid),
		log.Kv("roomId", room.roomId))
}

// 处理玩家取消匹配
func (m *AutoChessMatch) handlePlayerCancel(uid uint64) {
	// 从匹配队列中移除
	for _, queue := range m.matchQueues {
		queue.RemoveByKey(uid)
	}
	
	// 从房间中移除
	for _, rooms := range m.battleRooms {
		for roomId, room := range rooms {
			for i, player := range room.players {
				if player.battleInfo.Uid == uid {
					// 移除玩家
					room.players = append(room.players[:i], room.players[i+1:]...)
					room.currentCount--
					
					// 如果是创建者离开，解散房间
					if room.creatorUid == uid {
						delete(rooms, roomId)
						log.Info("Room disbanded due to creator leaving", 
							log.Kv("roomId", roomId))
					}
					goto found
				}
			}
		}
	}
found:
	log.Info("Player cancelled match", log.Kv("uid", uid))
}

// 处理匹配逻辑
func (m *AutoChessMatch) processMatching() {
	// 处理超时玩家
	m.processTimeoutPlayers()
	
	// 处理房间扩展匹配范围
	m.expandRoomMatchingRange()
	
	// 清理超时房间
	m.cleanupTimeoutRooms()
}

// 查找合适的房间
func (m *AutoChessMatch) findSuitableRoom(player *AutoChessPlayerInfo) *AutoChessBattleRoom {
	rooms, exists := m.battleRooms[player.serverGroup]
	if !exists {
		return nil
	}
	
	// 获取玩家的匹配范围
	floatingRanges := m.getFloatingRanges(player.battleInfo.Throphy)
	if len(floatingRanges) == 0 {
		return nil
	}
	
	// 使用第一个范围进行匹配（后续会根据时间扩展）
	minScore := player.battleInfo.Throphy + floatingRanges[0][0]
	maxScore := player.battleInfo.Throphy + floatingRanges[0][1]
	
	var suitableRooms []*AutoChessBattleRoom
	for _, room := range rooms {
		if room.state == 1 && // 匹配中
		   room.currentCount < room.playerCount &&
		   room.matchType == player.matchType &&
		   room.roomScore >= minScore && 
		   room.roomScore <= maxScore {
			suitableRooms = append(suitableRooms, room)
		}
	}
	
	// 随机选择一个合适的房间
	if len(suitableRooms) > 0 {
		return suitableRooms[rand.Intn(len(suitableRooms))]
	}
	
	return nil
}

// 加入房间
func (m *AutoChessMatch) joinRoom(room *AutoChessBattleRoom, player *AutoChessPlayerInfo) bool {
	if room.currentCount >= room.playerCount || room.state != 1 {
		return false
	}
	
	room.players = append(room.players, player)
	room.currentCount++
	
	return true
}

// 创建房间
func (m *AutoChessMatch) createRoom(player *AutoChessPlayerInfo) *AutoChessBattleRoom {
	roomId := fmt.Sprintf("autochess_%d_%d", player.battleInfo.Uid, time.Now().UnixNano())
	
	room := &AutoChessBattleRoom{
		roomId:       roomId,
		creatorUid:   player.battleInfo.Uid,
		roomScore:    player.battleInfo.Throphy,
		serverGroup:  player.serverGroup,
		matchType:    player.matchType,
		playerCount:  player.playerCount,
		currentCount: 1,
		players:      []*AutoChessPlayerInfo{player},
		createTime:   time.Now().Unix(),
		state:        1, // 匹配中
	}
	
	if m.battleRooms[player.serverGroup] == nil {
		m.battleRooms[player.serverGroup] = make(map[string]*AutoChessBattleRoom)
	}
	m.battleRooms[player.serverGroup][roomId] = room
	
	return room
}

// 开始战斗
func (m *AutoChessMatch) startBattle(room *AutoChessBattleRoom) {
	if room.state != 1 { // 不是匹配中状态
		return
	}

	room.state = 2 // 已满员

	// 创建战斗请求
	req := natsrpc.CreateBattleReq{
		CreateInfo: &natsrpc.PBCreateBattleInfo{
			Players: make([]*public.PBBattlePlayerInfo, 0, len(room.players)),
			Teams:   make([]*public.PBBattleTeamInfo, 0, len(room.players)),
		},
	}

	for _, player := range room.players {
		req.CreateInfo.Players = append(req.CreateInfo.Players, player.battleInfo)
		req.CreateInfo.Teams = append(req.CreateInfo.Teams, player.teamInfo)
	}

	// 异步调用BattleServer创建战斗
	global.BattleRpcClient.AsyncCreateBattle(context.Background(), &req, func(response *natsrpc.CreateBattleResp, err error) {
		if err != nil {
			log.Error("Failed to create autochess battle", log.Err(err), log.Kv("roomId", room.roomId))
			// 重置房间状态，允许重新匹配
			room.state = 1
			return
		}

		// 通知所有玩家匹配成功
		m.notifyMatchResult(room, response.BattleId, response.ServerId)

		// 标记房间已开始，准备清理
		room.state = 3 // 已开始

		log.Info("AutoChess battle created successfully",
			log.Kv("roomId", room.roomId),
			log.Kv("battleId", response.BattleId),
			log.Kv("serverId", response.ServerId))
	})
}

// 通知匹配结果
func (m *AutoChessMatch) notifyMatchResult(room *AutoChessBattleRoom, battleId int64, serverId int64) {
	targets := make([]*public.PBBattlePlayerInfo, 0, len(room.players))
	for _, player := range room.players {
		targets = append(targets, player.battleInfo)
	}

	// 通知每个玩家
	for _, player := range room.players {
		if err := global.GameRpcClient.MatchResult(context.Background(), &natsrpc.MatchResultRequest{
			Success:    true,
			Uid:        player.battleInfo.Uid,
			BattleId:   battleId,
			ServerId:   serverId,
			Target:     targets,
			CreateTime: time.Now().Unix(),
		}, player.battleInfo.ServerId); err != nil {
			log.Error("Failed to notify autochess match result",
				log.Err(err),
				log.Kv("uid", player.battleInfo.Uid),
				log.Kv("serverId", player.battleInfo.ServerId))
		}
	}
}

// 处理超时玩家
func (m *AutoChessMatch) processTimeoutPlayers() {
	currentTime := time.Now().Unix()

	for serverGroup, queue := range m.matchQueues {
		for e := queue.Front(); e != nil; {
			pair := e.Value.(*listmap.Pair)
			player := pair.V.(*AutoChessPlayerInfo)

			e = e.Next()

			waitTime := currentTime - player.beginTime

			// 根据匹配类型处理超时
			switch player.matchType {
			case AutoChessMatchTypePVE:
				if waitTime >= 10 { // 10秒后直接创建PVE战斗
					m.createPVEBattle(player)
					queue.RemoveByKey(player.battleInfo.Uid)
				}
			case AutoChessMatchTypePVPAsync:
				if waitTime >= 30 { // 30秒后创建异步PVP战斗
					m.createAsyncPVPBattle(player)
					queue.RemoveByKey(player.battleInfo.Uid)
				}
			case AutoChessMatchTypePVPComposite:
				if waitTime >= 60 { // 60秒后降级为异步PVP
					player.matchType = AutoChessMatchTypePVPAsync
					log.Info("Player match type downgraded to async PVP",
						log.Kv("uid", player.battleInfo.Uid))
				}
			}
		}
	}
}

// 扩展房间匹配范围
func (m *AutoChessMatch) expandRoomMatchingRange() {
	currentTime := time.Now().Unix()

	for _, rooms := range m.battleRooms {
		for _, room := range rooms {
			if room.state != 1 { // 不是匹配中状态
				continue
			}

			waitTime := currentTime - room.createTime

			// 60秒后如果只有创建者，考虑添加机器人
			if waitTime >= 60 && room.currentCount == 1 {
				m.fillRoomWithBots(room)
			}
		}
	}
}

// 用机器人填充房间
func (m *AutoChessMatch) fillRoomWithBots(room *AutoChessBattleRoom) {
	if room.state != 1 || room.currentCount >= room.playerCount {
		return
	}

	// 创建机器人玩家
	for room.currentCount < room.playerCount {
		bot := m.createBotPlayer(room.players[0]) // 基于第一个玩家创建机器人
		room.players = append(room.players, bot)
		room.currentCount++
	}

	log.Info("AutoChess room filled with bots",
		log.Kv("roomId", room.roomId),
		log.Kv("botCount", room.playerCount-1))

	// 开始战斗
	m.startBattle(room)
}

// 创建机器人玩家
func (m *AutoChessMatch) createBotPlayer(template *AutoChessPlayerInfo) *AutoChessPlayerInfo {
	botUid := uint64(time.Now().UnixNano()) // 临时UID

	return &AutoChessPlayerInfo{
		battleInfo: &public.PBBattlePlayerInfo{
			Uid:      botUid,
			Name:     fmt.Sprintf("AutoChessBot_%d", botUid%10000),
			ServerId: template.battleInfo.ServerId,
			Throphy:  template.battleInfo.Throphy + int32(rand.Intn(21)-10), // ±10分数范围
			Level:    template.battleInfo.Level,
		},
		teamInfo: &public.PBBattleTeamInfo{
			Heros: template.teamInfo.Heros, // 复制阵容
		},
		beginTime:   time.Now().Unix(),
		serverGroup: template.serverGroup,
		matchType:   template.matchType,
		playerCount: template.playerCount,
	}
}

// 创建PVE战斗
func (m *AutoChessMatch) createPVEBattle(player *AutoChessPlayerInfo) {
	// 创建3个机器人
	bots := make([]*AutoChessPlayerInfo, 3)
	for i := 0; i < 3; i++ {
		bots[i] = m.createBotPlayer(player)
	}

	// 创建战斗请求
	req := natsrpc.CreateBattleReq{
		CreateInfo: &natsrpc.PBCreateBattleInfo{
			Players: []*public.PBBattlePlayerInfo{player.battleInfo},
			Teams:   []*public.PBBattleTeamInfo{player.teamInfo},
		},
	}

	for _, bot := range bots {
		req.CreateInfo.Players = append(req.CreateInfo.Players, bot.battleInfo)
		req.CreateInfo.Teams = append(req.CreateInfo.Teams, bot.teamInfo)
	}

	// 异步创建战斗
	global.BattleRpcClient.AsyncCreateBattle(context.Background(), &req, func(response *natsrpc.CreateBattleResp, err error) {
		if err != nil {
			log.Error("Failed to create autochess PVE battle", log.Err(err), log.Kv("uid", player.battleInfo.Uid))
			return
		}

		// 通知玩家
		targets := []*public.PBBattlePlayerInfo{player.battleInfo}
		for _, bot := range bots {
			targets = append(targets, bot.battleInfo)
		}

		global.GameRpcClient.MatchResult(context.Background(), &natsrpc.MatchResultRequest{
			Success:    true,
			Uid:        player.battleInfo.Uid,
			BattleId:   response.BattleId,
			ServerId:   response.ServerId,
			Target:     targets,
			CreateTime: time.Now().Unix(),
		}, player.battleInfo.ServerId)

		log.Info("AutoChess PVE battle created",
			log.Kv("uid", player.battleInfo.Uid),
			log.Kv("battleId", response.BattleId))
	})
}

// 创建异步PVP战斗
func (m *AutoChessMatch) createAsyncPVPBattle(player *AutoChessPlayerInfo) {
	// 异步PVP逻辑：使用真实玩家数据但AI操作
	// TODO: 从数据库获取其他玩家的历史数据
	// 暂时用机器人代替
	m.createPVEBattle(player)
}

// 清理超时房间
func (m *AutoChessMatch) cleanupTimeoutRooms() {
	currentTime := time.Now().Unix()

	for serverGroup, rooms := range m.battleRooms {
		for roomId, room := range rooms {
			// 清理已开始超过5分钟的房间
			if room.state == 3 && currentTime-room.createTime > 300 {
				delete(rooms, roomId)
				log.Info("Cleaned up started autochess room", log.Kv("roomId", roomId))
			}
			// 清理匹配超过10分钟的空房间
			if room.state == 1 && room.currentCount == 0 && currentTime-room.createTime > 600 {
				delete(rooms, roomId)
				log.Info("Cleaned up empty autochess room", log.Kv("roomId", roomId))
			}
		}
	}
}

// 获取服务器组
func (m *AutoChessMatch) getServerGroup(serverId string) string {
	// TODO: 从serverList_Group表加载配置
	// 根据serverId查找对应的服务器组
	// 暂时使用默认逻辑
	return "default"
}

// 获取匹配类型
func (m *AutoChessMatch) getMatchType(trophy int32) int32 {
	// TODO: 根据奖杯数从MainRank_type表获取匹配类型
	// 暂时使用简单逻辑
	if trophy < 100 {
		return AutoChessMatchTypePVE
	} else if trophy < 500 {
		return AutoChessMatchTypePVPRealtime
	} else {
		return AutoChessMatchTypePVPComposite
	}
}

// 获取玩家数量配置
func (m *AutoChessMatch) getPlayerCount() int32 {
	// TODO: 从游戏配置读取，支持2人、4人等不同模式
	return 4 // 默认4人
}

// 获取分数浮动范围
func (m *AutoChessMatch) getFloatingRanges(trophy int32) [][]int32 {
	// TODO: 根据奖杯数从MainRank_Area表获取Area配置
	// 暂时使用默认范围
	return [][]int32{{-10, 10}, {-20, 20}, {-40, 40}}
}

// 获取当前应该使用的匹配范围索引
func (m *AutoChessMatch) getCurrentRangeIndex(waitTime int64) int {
	// TODO: 从setting表读取MainRank_MatchTime配置
	// 格式：[5,10,15,30] 对应不同的时间段
	matchTimeConfig := []int32{5, 10, 15, 30}

	for i, timeLimit := range matchTimeConfig {
		if waitTime < int64(timeLimit) {
			return i
		}
	}
	return len(matchTimeConfig) - 1 // 使用最大范围
}
