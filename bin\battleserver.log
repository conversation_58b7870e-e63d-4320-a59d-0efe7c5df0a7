[BattleService] Player 10106021303 (可靠的.穿越者) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000086053 (AI_可靠的.穿越者) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000049108 (AI_可靠的.穿越者) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000042465 (AI_可靠的.穿越者) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleStateManager_431145536454671] Initialized
[PlayerManager_431145536454671] Player 10106021303 (可靠的.穿越者) Trophy 1 Health 3
[PlayerManager_431145536454671] Player 90000086053 (AI_可靠的.穿越者) Trophy 1 Health 3
[PlayerManager_431145536454671] Player 90000049108 (AI_可靠的.穿越者) Trophy 1 Health 3
[PlayerManager_431145536454671] Player 90000042465 (AI_可靠的.穿越者) Trophy 1 Health 3
[PlayerManager_431145536454671] Initialized with 4 players
[BattleInstanceManager] Initialized for battle 431145536454671
[OpponentPairManager] Initialized for battle 431145536454671
[BuffManager_431145536454671] Initialized
[CheckerBoard_431145536454671] Cleared checkerboard
[CheckerBoard_431145536454671] Initialized
[AutoChessScene_431145536454671] Event handlers registered
[AutoChessScene_431145536454671] Battle 431145536454671 initialized with 4 players, waiting for all players to enter
[SceneManager] Added AutoChessScene 431145536454671 to thread management
[BattleService] Battle 431145536454671 created successfully with 4 players
[BattleService] Battle 431145536454671 is now waiting for all players to call EnterBattle RPC
[BattleService] Current battle state: StateNone (waiting for player entry)
[BattleService] Players expected to enter: [10106021303, 90000086053, 90000049108, 90000042465]
[BattleService] ===== IMPORTANT: Battle will NOT start until ALL players call EnterBattle =====
[BattleService] ===== GameServer should call EnterBattle for each player after scene loading =====
[BattleService] ===== BOT STRATEGY: Player 10106021303 is real player, others are bots =====
[BattleService] Auto-entered bot player 90000086053 immediately
[BattleService] Auto-entered bot player 90000049108 immediately
[BattleService] Auto-entered bot player 90000042465 immediately
[BattleService] Battle 431145536454671 bots auto-entered: 3/4 players ready
[BattleService] Status: 1 waiting (3/4 entered), 0 active
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10106021303 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10106021303 entered battle 431145536454671 (initial), current count: 4
[BattleService] All 4 players entered battle 431145536454671, starting battle state machine
[BattleService] Entered players: [90000086053, 90000049108, 90000042465, 10106021303]
[BattleService] Triggering AutoChessScene.StartBattleStateMachine() for battle 431145536454671
[AutoChessScene_431145536454671] StartBattleStateMachine() called for battle 431145536454671
[AutoChessScene_431145536454671] BattleStateManager is ready, starting first round...
[AutoChessScene_431145536454671] Current state before starting: StateNone
[BattleStateManager_431145536454671] ===== STARTING NEW ROUND 1 =====
[BattleStateManager_431145536454671] Round 1 has buff selection: False
[BattleStateManager_431145536454671] Publishing RoundStartedEvent for round 1
[AutoChessScene_431145536454671] Round 1 started
[BattleStateManager_431145536454671] Setting state to StateRoundStart for round 1
[BattleStateManager_431145536454671] State: StateNone -> StateRoundStart (R1, 1000ms)
[AutoChessScene_431145536454671] HandleRoundStart: 4 active players
[AutoChessScene_431145536454671] Valid player: 10106021303, Health: 3
[AutoChessScene_431145536454671] Valid player: 90000086053, Health: 3
[AutoChessScene_431145536454671] Valid player: 90000049108, Health: 3
[AutoChessScene_431145536454671] Valid player: 90000042465, Health: 3
[AutoChessScene_431145536454671] No instance found for player 10106021303 when saving board data
[AutoChessScene_431145536454671] No instance found for player 90000086053 when saving board data
[AutoChessScene_431145536454671] No instance found for player 90000049108 when saving board data
[AutoChessScene_431145536454671] No instance found for player 90000042465 when saving board data
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player 90000086053 vs Player 90000049108
[OpponentPairManager] Random pair: Player 90000042465 vs Player 10106021303
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_431145536454671] Created 4 opponent pairs
[PlayerManager_431145536454671] Set player opponents, count: 4
[BattleInstanceManager] Created instance 431145536454671_1 for active players 90000086053 vs 90000049108
[CheckerBoard_431145536454671] Cleared checkerboard
[CheckerBoard_431145536454671] Initialized
[BattleInstance] 431145536454671_1 created with players: 90000086053, 90000049108
[BattleInstanceManager] Created instance 431145536454671_2 for active players 90000042465 vs 10106021303
[CheckerBoard_431145536454671] Cleared checkerboard
[CheckerBoard_431145536454671] Initialized
[BattleInstance] 431145536454671_2 created with players: 90000042465, 10106021303
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_431145536454671] Cleaned orphaned entities for player 10106021303
[AutoChessScene_431145536454671] Cleaned orphaned entities for player 90000086053
[AutoChessScene_431145536454671] Cleaned orphaned entities for player 90000049108
[AutoChessScene_431145536454671] Cleaned orphaned entities for player 90000042465
[PlayerManager_431145536454671] Reset all players ready status
[AutoChessScene_431145536454671] Round started with 2 battle instances
[AutoChessScene_431145536454671] Generating 5 heroes for all players in round 1
[CheckerBoard_431145536454671] Placed entity 1 at position (6, 4)
[CheckerBoard_431145536454671] Created entity ID:1 ConfigID:101 at (6, 4) for player 10106021303
[CheckerBoard_431145536454671] Placed entity 2 at position (8, 1)
[CheckerBoard_431145536454671] Created entity ID:2 ConfigID:103 at (8, 1) for player 10106021303
[CheckerBoard_431145536454671] Placed entity 3 at position (6, 3)
[CheckerBoard_431145536454671] Created entity ID:3 ConfigID:101 at (6, 3) for player 10106021303
[CheckerBoard_431145536454671] Placed entity 4 at position (10, 4)
[CheckerBoard_431145536454671] Created entity ID:4 ConfigID:102 at (10, 4) for player 10106021303
[CheckerBoard_431145536454671] Placed entity 5 at position (6, 1)
[CheckerBoard_431145536454671] Created entity ID:5 ConfigID:103 at (6, 1) for player 10106021303
[CheckerBoard_431145536454671] Generated 5/5 heroes for player 10106021303: 5 placed, 0 in temporary slots
[CheckerBoard_431145536454671] Generated 5 heroes for player 10106021303 in Enemy area
[AutoChessScene_431145536454671] Generated 5 heroes for player 10106021303: 5 placed on board, 0 in temporary slots
[CheckerBoard_431145536454671] Placed entity 1 at position (3, 2)
[CheckerBoard_431145536454671] Created entity ID:1 ConfigID:102 at (3, 2) for player 90000086053
[CheckerBoard_431145536454671] Placed entity 2 at position (4, 6)
[CheckerBoard_431145536454671] Created entity ID:2 ConfigID:103 at (4, 6) for player 90000086053
[CheckerBoard_431145536454671] Placed entity 3 at position (1, 3)
[CheckerBoard_431145536454671] Created entity ID:3 ConfigID:101 at (1, 3) for player 90000086053
[CheckerBoard_431145536454671] Placed entity 4 at position (3, 1)
[CheckerBoard_431145536454671] Created entity ID:4 ConfigID:101 at (3, 1) for player 90000086053
[CheckerBoard_431145536454671] Placed entity 5 at position (5, 1)
[CheckerBoard_431145536454671] Created entity ID:5 ConfigID:101 at (5, 1) for player 90000086053
[CheckerBoard_431145536454671] Generated 5/5 heroes for player 90000086053: 5 placed, 0 in temporary slots
[CheckerBoard_431145536454671] Generated 5 heroes for player 90000086053 in My area
[AutoChessScene_431145536454671] Generated 5 heroes for player 90000086053: 5 placed on board, 0 in temporary slots
[CheckerBoard_431145536454671] Placed entity 6 at position (6, 1)
[CheckerBoard_431145536454671] Created entity ID:6 ConfigID:102 at (6, 1) for player 90000049108
[CheckerBoard_431145536454671] Placed entity 7 at position (10, 2)
[CheckerBoard_431145536454671] Created entity ID:7 ConfigID:103 at (10, 2) for player 90000049108
[CheckerBoard_431145536454671] Placed entity 8 at position (6, 3)
[CheckerBoard_431145536454671] Created entity ID:8 ConfigID:102 at (6, 3) for player 90000049108
[CheckerBoard_431145536454671] Placed entity 9 at position (10, 6)
[CheckerBoard_431145536454671] Created entity ID:9 ConfigID:101 at (10, 6) for player 90000049108
[CheckerBoard_431145536454671] Placed entity 10 at position (8, 2)
[CheckerBoard_431145536454671] Created entity ID:10 ConfigID:102 at (8, 2) for player 90000049108
[CheckerBoard_431145536454671] Generated 5/5 heroes for player 90000049108: 5 placed, 0 in temporary slots
[CheckerBoard_431145536454671] Generated 5 heroes for player 90000049108 in Enemy area
[AutoChessScene_431145536454671] Generated 5 heroes for player 90000049108: 5 placed on board, 0 in temporary slots
[CheckerBoard_431145536454671] Placed entity 6 at position (4, 4)
[CheckerBoard_431145536454671] Created entity ID:6 ConfigID:103 at (4, 4) for player 90000042465
[CheckerBoard_431145536454671] Placed entity 7 at position (2, 6)
[CheckerBoard_431145536454671] Created entity ID:7 ConfigID:103 at (2, 6) for player 90000042465
[CheckerBoard_431145536454671] Placed entity 8 at position (5, 2)
[CheckerBoard_431145536454671] Created entity ID:8 ConfigID:101 at (5, 2) for player 90000042465
[CheckerBoard_431145536454671] Placed entity 9 at position (2, 3)
[CheckerBoard_431145536454671] Created entity ID:9 ConfigID:102 at (2, 3) for player 90000042465
[CheckerBoard_431145536454671] Placed entity 10 at position (2, 5)
[CheckerBoard_431145536454671] Created entity ID:10 ConfigID:103 at (2, 5) for player 90000042465
[CheckerBoard_431145536454671] Generated 5/5 heroes for player 90000042465: 5 placed, 0 in temporary slots
[CheckerBoard_431145536454671] Generated 5 heroes for player 90000042465 in My area
[AutoChessScene_431145536454671] Generated 5 heroes for player 90000042465: 5 placed on board, 0 in temporary slots
[AutoChessScene_431145536454671] Player status: Total=4, Active=4
[AutoChessScene_431145536454671] Player 10106021303: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431145536454671] Player 90000086053: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431145536454671] Player 90000049108: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431145536454671] Player 90000042465: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431145536454671] Sending RoundStart notifications to 4 active players...
[AutoChessScene_431145536454671] RoundStart board data: player:90000042465 heroes:5
[AutoChessScene_431145536454671] RoundStart board data: player:10106021303 heroes:5
[AutoChessScene_431145536454671] Sending RoundStart to Player 10106021303 on GameServer 10102
[AutoChessScene_431145536454671] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431145536454671] RoundStart board data: player:90000086053 heroes:5
[AutoChessScene_431145536454671] RoundStart board data: player:90000049108 heroes:5
[AutoChessScene_431145536454671] Sending RoundStart to Player 90000086053 on GameServer 10102
[AutoChessScene_431145536454671] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431145536454671] RoundStart board data: player:90000086053 heroes:5
[AutoChessScene_431145536454671] RoundStart board data: player:90000049108 heroes:5
[AutoChessScene_431145536454671] Sending RoundStart to Player 90000049108 on GameServer 10102
[AutoChessScene_431145536454671] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431145536454671] RoundStart board data: player:90000042465 heroes:5
[AutoChessScene_431145536454671] RoundStart board data: player:10106021303 heroes:5
[AutoChessScene_431145536454671] Sending RoundStart to Player 90000042465 on GameServer 10102
[AutoChessScene_431145536454671] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431145536454671] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 431145536454671 state to StateRoundStart
[AutoChessScene_431145536454671] State change sent to GameServer: StateNone -> StateRoundStart (R1)
[BattleStateManager_431145536454671] BattleStateChangedEvent published successfully
[BattleStateManager_431145536454671] ===== ROUND 1 INITIALIZATION COMPLETE =====
[AutoChessScene_431145536454671] Battle state machine started successfully for battle 431145536454671
[AutoChessScene_431145536454671] Current state after starting: StateRoundStart
[BattleService] Battle state update result: False, new state: StateRoundStart
[BattleStateManager_431145536454671] State: StateRoundStart -> StatePreparation (R1, 65000ms)
[AutoChessScene_431145536454671] Preparation phase started
[PlayerManager_431145536454671] Player 90000086053 ready status set to True
[PlayerManager_431145536454671] Player 90000049108 ready status set to True
[PlayerManager_431145536454671] Player 90000042465 ready status set to True
[AutoChessScene_431145536454671] Auto-ready 3 additional bots
[AutoChessScene_431145536454671] Free operation phase started
[BattleService] Updated battle 431145536454671 state to StatePreparation
[AutoChessScene_431145536454671] State change sent to GameServer: StateRoundStart -> StatePreparation (R1)
[BattleStateManager_431145536454671] BattleStateChangedEvent published successfully
