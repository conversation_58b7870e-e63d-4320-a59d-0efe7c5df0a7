"G:\JetBrains\JetBrains Rider 2024.3.5\plugins\dpa\DotFiles\JetBrains.DPA.Runner.exe" --handle=26216 --backend-pid=21088 --etw-collect-flags=67108622 --detach-event-name=dpa.detach.21088.40 --refresh-interval=1 -- G:/ZZZ/src/Main/Server/liteframe/bin/BattleServer.exe
MainThread Start.
MainThread LoadAndInitAppAssembly Start...
MainThread LoadAndInitAppAssembly End...
ThreadManager init begin.
ThreadManager init end.
[WorkUnit] WorkUnit 1 Start With Owner GlobalManager_ThreadManager.
=== 环境变量 ===
DOTNET_gcServer=
DOTNET_GCHeapCount: 
DOTNET_gcConcurrent: 
DOTNET_GCDynamicAdaptationMode: 
===================
=== .NET GC 配置 ===
GC 模式: Workstation GC
GC的LatencyMode模式: Interactive
LOH 压缩模式: Default
===================
BattleConfig initialized with PlayMode ID: 1, Type: 1, Scene: 1001
BattleConfig initialized successfully
NATS client configuration not found, using server connection for client
Status check timer started (10 second interval)
-------------【服务器程序加载完毕】-----------------------
------------------------------------------------------
[WorkUnit] WorkUnit 1 Enter Thread 0.
SceneManager Start
[WorkUnit] WorkUnit 2 Start With Owner GlobalManager_SceneManager.
[WorkUnit] WorkUnit 2 Enter Thread 31.
[BattleService] Player 10106021303 (可靠的.穿越者) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000080171 (AI_可靠的.穿越者) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000075484 (AI_可靠的.穿越者) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000042297 (AI_可靠的.穿越者) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleStateManager_431799129079809] Initialized
[PlayerManager_431799129079809] Player 10106021303 (可靠的.穿越者) Trophy 1 Health 3
[PlayerManager_431799129079809] Player 90000080171 (AI_可靠的.穿越者) Trophy 1 Health 3
[PlayerManager_431799129079809] Player 90000075484 (AI_可靠的.穿越者) Trophy 1 Health 3
[PlayerManager_431799129079809] Player 90000042297 (AI_可靠的.穿越者) Trophy 1 Health 3
[PlayerManager_431799129079809] Initialized with 4 players
[BattleInstanceManager] Initialized for battle 431799129079809
[OpponentPairManager] Initialized for battle 431799129079809
[BuffManager_431799129079809] Initialized
[CheckerBoard_431799129079809] Cleared checkerboard
[CheckerBoard_431799129079809] Initialized
[AutoChessScene_431799129079809] Event handlers registered
[AutoChessScene_431799129079809] Battle 431799129079809 initialized with 4 players, waiting for all players to enter
[SceneManager] Added AutoChessScene 431799129079809 to thread management
[BattleService] Battle 431799129079809 created successfully with 4 players
[BattleService] Battle 431799129079809 is now waiting for all players to call EnterBattle RPC
[BattleService] Current battle state: StateNone (waiting for player entry)
[BattleService] Players expected to enter: [10106021303, 90000080171, 90000075484, 90000042297]
[BattleService] ===== IMPORTANT: Battle will NOT start until ALL players call EnterBattle =====
[BattleService] ===== GameServer should call EnterBattle for each player after scene loading =====
[BattleService] ===== BOT STRATEGY: Player 10106021303 is real player, others are bots =====
[BattleService] Auto-entered bot player 90000080171 immediately
[BattleService] Auto-entered bot player 90000075484 immediately
[BattleService] Auto-entered bot player 90000042297 immediately
[BattleService] Battle 431799129079809 bots auto-entered: 3/4 players ready
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10106021303 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10106021303 entered battle 431799129079809 (initial), current count: 4
[BattleService] All 4 players entered battle 431799129079809, starting battle state machine
[BattleService] Entered players: [90000080171, 90000075484, 90000042297, 10106021303]
[BattleService] Triggering AutoChessScene.StartBattleStateMachine() for battle 431799129079809
[AutoChessScene_431799129079809] StartBattleStateMachine() called for battle 431799129079809
[AutoChessScene_431799129079809] BattleStateManager is ready, starting first round...
[AutoChessScene_431799129079809] Current state before starting: StateNone
[BattleStateManager_431799129079809] ===== STARTING NEW ROUND 1 =====
[BattleStateManager_431799129079809] Round 1 has buff selection: False
[BattleStateManager_431799129079809] Publishing RoundStartedEvent for round 1
[AutoChessScene_431799129079809] Round 1 started
[BattleStateManager_431799129079809] Setting state to StateRoundStart for round 1
[BattleStateManager_431799129079809] State: StateNone -> StateRoundStart (R1, 1000ms)
[AutoChessScene_431799129079809] HandleRoundStart: 4 active players
[AutoChessScene_431799129079809] Valid player: 10106021303, Health: 3
[AutoChessScene_431799129079809] Valid player: 90000080171, Health: 3
[AutoChessScene_431799129079809] Valid player: 90000075484, Health: 3
[AutoChessScene_431799129079809] Valid player: 90000042297, Health: 3
[AutoChessScene_431799129079809] No instance found for player 10106021303 when saving board data
[AutoChessScene_431799129079809] No instance found for player 90000080171 when saving board data
[AutoChessScene_431799129079809] No instance found for player 90000075484 when saving board data
[AutoChessScene_431799129079809] No instance found for player 90000042297 when saving board data
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player 90000080171 vs Player 90000042297
[OpponentPairManager] Random pair: Player 90000075484 vs Player 10106021303
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_431799129079809] Created 4 opponent pairs
[PlayerManager_431799129079809] Set player opponents, count: 4
[BattleInstanceManager] Created instance 431799129079809_1 for active players 90000080171 vs 90000042297
[CheckerBoard_431799129079809] Cleared checkerboard
[CheckerBoard_431799129079809] Initialized
[BattleInstance] 431799129079809_1 created with players: 90000080171, 90000042297
[BattleInstanceManager] Created instance 431799129079809_2 for active players 90000075484 vs 10106021303
[CheckerBoard_431799129079809] Cleared checkerboard
[CheckerBoard_431799129079809] Initialized
[BattleInstance] 431799129079809_2 created with players: 90000075484, 10106021303
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 10106021303
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 90000080171
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 90000075484
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 90000042297
[PlayerManager_431799129079809] Reset all players ready status
[AutoChessScene_431799129079809] Round started with 2 battle instances
[AutoChessScene_431799129079809] Generating 5 heroes for all players in round 1
[CheckerBoard_431799129079809] Placed entity 1 at position (9, 6)
[CheckerBoard_431799129079809] Created entity ID:1 ConfigID:102 at (9, 6) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 2 at position (10, 5)
[CheckerBoard_431799129079809] Created entity ID:2 ConfigID:103 at (10, 5) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 3 at position (7, 5)
[CheckerBoard_431799129079809] Created entity ID:3 ConfigID:103 at (7, 5) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 4 at position (7, 4)
[CheckerBoard_431799129079809] Created entity ID:4 ConfigID:101 at (7, 4) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 5 at position (9, 5)
[CheckerBoard_431799129079809] Created entity ID:5 ConfigID:102 at (9, 5) for player 10106021303
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 10106021303: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 10106021303 in Enemy area
[AutoChessScene_431799129079809] Generated 5 heroes for player 10106021303: 5 placed on board, 0 in temporary slots
[CheckerBoard_431799129079809] Placed entity 1 at position (1, 4)
[CheckerBoard_431799129079809] Created entity ID:1 ConfigID:101 at (1, 4) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 2 at position (4, 1)
[CheckerBoard_431799129079809] Created entity ID:2 ConfigID:101 at (4, 1) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 3 at position (3, 5)
[CheckerBoard_431799129079809] Created entity ID:3 ConfigID:101 at (3, 5) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 4 at position (3, 6)
[CheckerBoard_431799129079809] Created entity ID:4 ConfigID:101 at (3, 6) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 5 at position (1, 1)
[CheckerBoard_431799129079809] Created entity ID:5 ConfigID:102 at (1, 1) for player 90000080171
[CheckerBoard_431799129079809] CheckTimes limit (4) reached for 1 hero types for player 90000080171
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 90000080171: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 90000080171 in My area
[AutoChessScene_431799129079809] Generated 5 heroes for player 90000080171: 5 placed on board, 0 in temporary slots
[CheckerBoard_431799129079809] Placed entity 6 at position (5, 4)
[CheckerBoard_431799129079809] Created entity ID:6 ConfigID:102 at (5, 4) for player 90000075484
[CheckerBoard_431799129079809] Placed entity 7 at position (5, 2)
[CheckerBoard_431799129079809] Created entity ID:7 ConfigID:103 at (5, 2) for player 90000075484
[CheckerBoard_431799129079809] Placed entity 8 at position (1, 1)
[CheckerBoard_431799129079809] Created entity ID:8 ConfigID:103 at (1, 1) for player 90000075484
[CheckerBoard_431799129079809] Placed entity 9 at position (4, 2)
[CheckerBoard_431799129079809] Created entity ID:9 ConfigID:102 at (4, 2) for player 90000075484
[CheckerBoard_431799129079809] Placed entity 10 at position (4, 3)
[CheckerBoard_431799129079809] Created entity ID:10 ConfigID:103 at (4, 3) for player 90000075484
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 90000075484: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 90000075484 in My area
[AutoChessScene_431799129079809] Generated 5 heroes for player 90000075484: 5 placed on board, 0 in temporary slots
[CheckerBoard_431799129079809] Placed entity 6 at position (9, 3)
[CheckerBoard_431799129079809] Created entity ID:6 ConfigID:101 at (9, 3) for player 90000042297
[CheckerBoard_431799129079809] Placed entity 7 at position (10, 6)
[CheckerBoard_431799129079809] Created entity ID:7 ConfigID:103 at (10, 6) for player 90000042297
[CheckerBoard_431799129079809] Placed entity 8 at position (6, 6)
[CheckerBoard_431799129079809] Created entity ID:8 ConfigID:102 at (6, 6) for player 90000042297
[CheckerBoard_431799129079809] Placed entity 9 at position (9, 2)
[CheckerBoard_431799129079809] Created entity ID:9 ConfigID:101 at (9, 2) for player 90000042297
[CheckerBoard_431799129079809] Placed entity 10 at position (6, 2)
[CheckerBoard_431799129079809] Created entity ID:10 ConfigID:101 at (6, 2) for player 90000042297
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 90000042297: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 90000042297 in Enemy area
[AutoChessScene_431799129079809] Generated 5 heroes for player 90000042297: 5 placed on board, 0 in temporary slots
[AutoChessScene_431799129079809] Player status: Total=4, Active=4
[AutoChessScene_431799129079809] Player 10106021303: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431799129079809] Player 90000080171: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431799129079809] Player 90000075484: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431799129079809] Player 90000042297: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431799129079809] Sending RoundStart notifications to 4 active players...
[AutoChessScene_431799129079809] RoundStart board data: player:90000075484 heroes:5
[AutoChessScene_431799129079809] RoundStart board data: player:10106021303 heroes:5
[AutoChessScene_431799129079809] Sending RoundStart to Player 10106021303 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431799129079809] RoundStart board data: player:90000080171 heroes:5
[AutoChessScene_431799129079809] RoundStart board data: player:90000042297 heroes:5
[AutoChessScene_431799129079809] Sending RoundStart to Player 90000080171 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431799129079809] RoundStart board data: player:90000075484 heroes:5
[AutoChessScene_431799129079809] RoundStart board data: player:10106021303 heroes:5
[AutoChessScene_431799129079809] Sending RoundStart to Player 90000075484 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431799129079809] RoundStart board data: player:90000080171 heroes:5
[AutoChessScene_431799129079809] RoundStart board data: player:90000042297 heroes:5
[AutoChessScene_431799129079809] Sending RoundStart to Player 90000042297 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431799129079809] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 431799129079809 state to StateRoundStart
[AutoChessScene_431799129079809] State change sent to GameServer: StateNone -> StateRoundStart (R1)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleStateManager_431799129079809] ===== ROUND 1 INITIALIZATION COMPLETE =====
[AutoChessScene_431799129079809] Battle state machine started successfully for battle 431799129079809
[AutoChessScene_431799129079809] Current state after starting: StateRoundStart
[BattleService] Battle state update result: False, new state: StateRoundStart
[BattleStateManager_431799129079809] State: StateRoundStart -> StatePreparation (R1, 65000ms)
[AutoChessScene_431799129079809] Preparation phase started
[PlayerManager_431799129079809] Player 90000080171 ready status set to True
[PlayerManager_431799129079809] Player 90000075484 ready status set to True
[PlayerManager_431799129079809] Player 90000042297 ready status set to True
[AutoChessScene_431799129079809] Auto-ready 3 additional bots
[AutoChessScene_431799129079809] Free operation phase started
[BattleService] Updated battle 431799129079809 state to StatePreparation
[AutoChessScene_431799129079809] State change sent to GameServer: StateRoundStart -> StatePreparation (R1)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[AutoChessScene_431799129079809] MergeHero operation: Player 10106021303, From GridID 53 → To GridID 54
[CheckerBoard_431799129079809] Merged entity from (9, 5) to (9, 6). Star level increased from 1 to 2
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[PlayerManager_431799129079809] Player 10106021303 ready status set to True
[PlayerManager_431799129079809] All players are ready!
[AutoChessScene_431799129079809] All players are ready, transitioning to next state
[BattleStateManager_431799129079809] State: StatePreparation -> StateBattleStarting (R1, 1000ms)
[AutoChessScene_431799129079809] Applying battle start buffs for all players
[AutoChessScene_431799129079809] Camp info for player 90000075484: 5 heroes added
[AutoChessScene_431799129079809] Camp info for player 10106021303: 4 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 10106021303, Team order: [90000075484, 10106021303], total GridIDs used: 9
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 10106021303 vs Opponent 90000075484 with 2 teams
[AutoChessScene_431799129079809] Camp info for player 90000080171: 5 heroes added
[AutoChessScene_431799129079809] Camp info for player 90000042297: 5 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 90000080171, Team order: [90000080171, 90000042297], total GridIDs used: 10
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 90000080171 vs Opponent 90000042297 with 2 teams
[AutoChessScene_431799129079809] Camp info for player 90000075484: 5 heroes added
[AutoChessScene_431799129079809] Camp info for player 10106021303: 4 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 90000075484, Team order: [90000075484, 10106021303], total GridIDs used: 9
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 90000075484 vs Opponent 10106021303 with 2 teams
[AutoChessScene_431799129079809] Camp info for player 90000080171: 5 heroes added
[AutoChessScene_431799129079809] Camp info for player 90000042297: 5 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 90000042297, Team order: [90000080171, 90000042297], total GridIDs used: 10
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 90000042297 vs Opponent 90000080171 with 2 teams
[AutoChessScene_431799129079809] Sent RoundBattleStart notifications with seed: 269658736
[BattleService] Updated battle 431799129079809 state to StateBattleStarting
[AutoChessScene_431799129079809] State change sent to GameServer: StatePreparation -> StateBattleStarting (R1)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[AutoChessScene_431799129079809] Player 10106021303 set ready status to True
[BattleStateManager_431799129079809] State: StateBattleStarting -> StateBattleInProgress (R1, 65000ms)
[AutoChessScene_431799129079809] Starting all battle instances
[BattleInstance] 431799129079809_1 battle started
[BattleInstance] 431799129079809_2 battle started
[BattleInstanceManager] Started all 2 battle instances
[AutoChessScene_431799129079809] Auto EndBattle for bot 90000080171 vs bot 90000042297, random result: bot 90000080171 wins = True
[AutoChessScene_431799129079809] Player 90000080171 sent EndBattleReq (win: True), instance: 431799129079809_1
[AutoChessScene_431799129079809] Waiting for opponent 90000042297 to send EndBattleReq for instance 431799129079809_1
[AutoChessScene_431799129079809] Player 90000042297 sent EndBattleReq (win: False), instance: 431799129079809_1
[BattleInstance] 431799129079809_1 battle finished, winner: 90000080171, loser: 90000042297
[AutoChessScene_431799129079809] Battle instance 431799129079809_1 completed: Winner 90000080171, Loser 90000042297
[AutoChessScene_431799129079809] Bot 90000075484 vs real player 10106021303, waiting for real player result
[AutoChessScene_431799129079809] Bot vs real player battles will be handled by system timeout (65s)
[BattleService] Updated battle 431799129079809 state to StateBattleInProgress
[AutoChessScene_431799129079809] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R1)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleService] EndBattle uid: 10106021303, win: True
[AutoChessScene_431799129079809] Player 10106021303 sent EndBattleReq (win: True), instance: 431799129079809_2
[AutoChessScene_431799129079809] Auto EndBattle for bot 90000075484 vs real player 10106021303, bot result: False
[BattleInstance] 431799129079809_2 battle finished, winner: 10106021303, loser: 90000075484
[AutoChessScene_431799129079809] Battle instance 431799129079809_2 completed: Winner 10106021303, Loser 90000075484
[AutoChessScene_431799129079809] All battle instances finished, proceeding to settlement
[BattleStateManager_431799129079809] State: StateBattleInProgress -> StateRoundSettlement (R1, 5000ms)
[AutoChessScene_431799129079809] Processing battle results
[PlayerManager_431799129079809] Player 90000042297 health reduced by 1, current health: 2
[AutoChessScene_431799129079809] Player 90000042297 lost 1 health, winner: 90000080171
[AutoChessScene_431799129079809] Sent RoundBattleEnd notification (isEnd=false) - Winner: 90000080171, Loser: 90000042297
[PlayerManager_431799129079809] Player 90000075484 health reduced by 1, current health: 2
[AutoChessScene_431799129079809] Player 90000075484 lost 1 health, winner: 10106021303
[AutoChessScene_431799129079809] Sent RoundBattleEnd notification (isEnd=false) - Winner: 10106021303, Loser: 90000075484
[AutoChessScene_431799129079809] Checking players elimination
[AutoChessScene_431799129079809] Active players remaining: 4
[AutoChessScene_431799129079809] Waiting for all players to confirm round settlement before starting new round
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000080171
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000075484
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000042297
[BattleService] Updated battle 431799129079809 state to StateRoundSettlement
[AutoChessScene_431799129079809] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R1)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10106021303 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10106021303 confirmed round settlement, current state: StateRoundSettlement
[AutoChessScene_431799129079809] Player 10106021303 confirmed round settlement, count: 4
[AutoChessScene_431799129079809] Real player 10106021303 confirmed, auto-confirming all bots
[AutoChessScene_431799129079809] All players confirmed round settlement, starting new round
[PlayerManager_431799129079809] Player 10106021303 ready status set to False
[PlayerManager_431799129079809] Player 90000080171 ready status set to False
[PlayerManager_431799129079809] Player 90000075484 ready status set to False
[PlayerManager_431799129079809] Player 90000042297 ready status set to False
[BattleStateManager_431799129079809] ===== STARTING NEW ROUND 2 =====
[BattleStateManager_431799129079809] Round 2 has buff selection: True
[BattleStateManager_431799129079809] Publishing RoundStartedEvent for round 2
[AutoChessScene_431799129079809] Round 2 started
[BattleStateManager_431799129079809] Setting state to StateRoundStart for round 2
[BattleStateManager_431799129079809] State: StateRoundSettlement -> StateRoundStart (R2, 1000ms)
[AutoChessScene_431799129079809] HandleRoundStart: 4 active players
[AutoChessScene_431799129079809] Valid player: 10106021303, Health: 3
[AutoChessScene_431799129079809] Valid player: 90000080171, Health: 3
[AutoChessScene_431799129079809] Valid player: 90000075484, Health: 2
[AutoChessScene_431799129079809] Valid player: 90000042297, Health: 2
[AutoChessScene_431799129079809] Player 10106021303 has 4 entities to save
[PlayerManager_431799129079809] Saved board data: player:10106021303 entities:4
[PlayerManager_431799129079809] Saved prev round data: player:10106021303 entities:4
[AutoChessScene_431799129079809] Player 90000080171 has 5 entities to save
[PlayerManager_431799129079809] Saved board data: player:90000080171 entities:5
[PlayerManager_431799129079809] Saved prev round data: player:90000080171 entities:5
[AutoChessScene_431799129079809] Player 90000075484 has 5 entities to save
[PlayerManager_431799129079809] Saved board data: player:90000075484 entities:5
[PlayerManager_431799129079809] Saved prev round data: player:90000075484 entities:5
[AutoChessScene_431799129079809] Player 90000042297 has 5 entities to save
[PlayerManager_431799129079809] Saved board data: player:90000042297 entities:5
[PlayerManager_431799129079809] Saved prev round data: player:90000042297 entities:5
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player 90000080171 vs Player 90000075484
[OpponentPairManager] Random pair: Player 90000042297 vs Player 10106021303
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_431799129079809] Created 4 opponent pairs
[PlayerManager_431799129079809] Set player opponents, count: 4
[BattleInstanceManager] Created instance 431799129079809_1 for active players 90000080171 vs 90000075484
[CheckerBoard_431799129079809] Cleared checkerboard
[CheckerBoard_431799129079809] Initialized
[BattleInstance] 431799129079809_1 created with players: 90000080171, 90000075484
[BattleInstanceManager] Created instance 431799129079809_2 for active players 90000042297 vs 10106021303
[CheckerBoard_431799129079809] Cleared checkerboard
[CheckerBoard_431799129079809] Initialized
[BattleInstance] 431799129079809_2 created with players: 90000042297, 10106021303
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_431799129079809] Restoring player 10106021303 to Enemy area (rows 6-10) based on current instance position
[CheckerBoard_431799129079809] Placed entity 1 at position (6, 1)
[CheckerBoard_431799129079809] Created entity ID:1 ConfigID:101 at (6, 1) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 1: (7,4)->(6,1), GridID:40->31
[CheckerBoard_431799129079809] Placed entity 2 at position (6, 2)
[CheckerBoard_431799129079809] Created entity ID:2 ConfigID:103 at (6, 2) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 2: (7,5)->(6,2), GridID:41->32
[CheckerBoard_431799129079809] Placed entity 3 at position (6, 3)
[CheckerBoard_431799129079809] Created entity ID:3 ConfigID:102 at (6, 3) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 3: (9,6)->(6,3), GridID:54->33
[CheckerBoard_431799129079809] Placed entity 4 at position (6, 4)
[CheckerBoard_431799129079809] Created entity ID:4 ConfigID:103 at (6, 4) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 4: (10,5)->(6,4), GridID:59->34
[AutoChessScene_431799129079809] Restored board: player:10106021303 entities:4/4
[AutoChessScene_431799129079809] Restoring player 90000080171 to My area (rows 1-5) based on current instance position
[CheckerBoard_431799129079809] Placed entity 1 at position (1, 1)
[CheckerBoard_431799129079809] Created entity ID:1 ConfigID:102 at (1, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 1: (1,1)->(1,1), GridID:1->1
[CheckerBoard_431799129079809] Placed entity 2 at position (1, 2)
[CheckerBoard_431799129079809] Created entity ID:2 ConfigID:101 at (1, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 2: (1,4)->(1,2), GridID:4->2
[CheckerBoard_431799129079809] Placed entity 3 at position (1, 3)
[CheckerBoard_431799129079809] Created entity ID:3 ConfigID:101 at (1, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 3: (3,5)->(1,3), GridID:17->3
[CheckerBoard_431799129079809] Placed entity 4 at position (1, 4)
[CheckerBoard_431799129079809] Created entity ID:4 ConfigID:101 at (1, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 4: (3,6)->(1,4), GridID:18->4
[CheckerBoard_431799129079809] Placed entity 5 at position (1, 5)
[CheckerBoard_431799129079809] Created entity ID:5 ConfigID:101 at (1, 5) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 5: (4,1)->(1,5), GridID:19->5
[AutoChessScene_431799129079809] Restored board: player:90000080171 entities:5/5
[AutoChessScene_431799129079809] Restoring player 90000075484 to Enemy area (rows 6-10) based on current instance position
[CheckerBoard_431799129079809] Placed entity 6 at position (6, 1)
[CheckerBoard_431799129079809] Created entity ID:6 ConfigID:103 at (6, 1) for player 90000075484
[AutoChessScene_431799129079809] Restored entity 6: (1,1)->(6,1), GridID:1->31
[CheckerBoard_431799129079809] Placed entity 7 at position (6, 2)
[CheckerBoard_431799129079809] Created entity ID:7 ConfigID:102 at (6, 2) for player 90000075484
[AutoChessScene_431799129079809] Restored entity 7: (4,2)->(6,2), GridID:20->32
[CheckerBoard_431799129079809] Placed entity 8 at position (6, 3)
[CheckerBoard_431799129079809] Created entity ID:8 ConfigID:103 at (6, 3) for player 90000075484
[AutoChessScene_431799129079809] Restored entity 8: (4,3)->(6,3), GridID:21->33
[CheckerBoard_431799129079809] Placed entity 9 at position (6, 4)
[CheckerBoard_431799129079809] Created entity ID:9 ConfigID:103 at (6, 4) for player 90000075484
[AutoChessScene_431799129079809] Restored entity 9: (5,2)->(6,4), GridID:26->34
[CheckerBoard_431799129079809] Placed entity 10 at position (6, 5)
[CheckerBoard_431799129079809] Created entity ID:10 ConfigID:102 at (6, 5) for player 90000075484
[AutoChessScene_431799129079809] Restored entity 10: (5,4)->(6,5), GridID:28->35
[AutoChessScene_431799129079809] Restored board: player:90000075484 entities:5/5
[AutoChessScene_431799129079809] Restoring player 90000042297 to My area (rows 1-5) based on current instance position
[CheckerBoard_431799129079809] Placed entity 5 at position (1, 1)
[CheckerBoard_431799129079809] Created entity ID:5 ConfigID:101 at (1, 1) for player 90000042297
[AutoChessScene_431799129079809] Restored entity 5: (6,2)->(1,1), GridID:32->1
[CheckerBoard_431799129079809] Placed entity 6 at position (1, 2)
[CheckerBoard_431799129079809] Created entity ID:6 ConfigID:102 at (1, 2) for player 90000042297
[AutoChessScene_431799129079809] Restored entity 6: (6,6)->(1,2), GridID:36->2
[CheckerBoard_431799129079809] Placed entity 7 at position (1, 3)
[CheckerBoard_431799129079809] Created entity ID:7 ConfigID:101 at (1, 3) for player 90000042297
[AutoChessScene_431799129079809] Restored entity 7: (9,2)->(1,3), GridID:50->3
[CheckerBoard_431799129079809] Placed entity 8 at position (1, 4)
[CheckerBoard_431799129079809] Created entity ID:8 ConfigID:101 at (1, 4) for player 90000042297
[AutoChessScene_431799129079809] Restored entity 8: (9,3)->(1,4), GridID:51->4
[CheckerBoard_431799129079809] Placed entity 9 at position (1, 5)
[CheckerBoard_431799129079809] Created entity ID:9 ConfigID:103 at (1, 5) for player 90000042297
[AutoChessScene_431799129079809] Restored entity 9: (10,6)->(1,5), GridID:60->5
[AutoChessScene_431799129079809] Restored board: player:90000042297 entities:5/5
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 10106021303
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 90000080171
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 90000075484
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 90000042297
[PlayerManager_431799129079809] Reset all players ready status
[AutoChessScene_431799129079809] Round started with 2 battle instances
[AutoChessScene_431799129079809] Generating buff options for all players
[BuffManager_431799129079809] Generated 3 buff options for player 10106021303: [105, 110, 103]
[AutoChessScene_431799129079809] Generated 3 buff options for player 10106021303: [105, 110, 103]
[BuffManager_431799129079809] Generated 3 buff options for player 90000080171: [104, 101, 109]
[AutoChessScene_431799129079809] Generated 3 buff options for player 90000080171: [104, 101, 109]
[BuffManager_431799129079809] Generated 3 buff options for player 90000075484: [109, 107, 110]
[AutoChessScene_431799129079809] Generated 3 buff options for player 90000075484: [109, 107, 110]
[BuffManager_431799129079809] Generated 3 buff options for player 90000042297: [104, 108, 101]
[AutoChessScene_431799129079809] Generated 3 buff options for player 90000042297: [104, 108, 101]
[AutoChessScene_431799129079809] Player status: Total=4, Active=4
[AutoChessScene_431799129079809] Player 10106021303: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431799129079809] Player 90000080171: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431799129079809] Player 90000075484: Eliminated=False, Health=2, HasInstance=True
[AutoChessScene_431799129079809] Player 90000042297: Eliminated=False, Health=2, HasInstance=True
[AutoChessScene_431799129079809] Sending RoundStart notifications to 4 active players...
[AutoChessScene_431799129079809] RoundStart: Player 10106021303 buff options: [105, 110, 103]
[AutoChessScene_431799129079809] RoundStart board data: player:90000042297 heroes:5
[AutoChessScene_431799129079809] RoundStart board data: player:10106021303 heroes:4
[AutoChessScene_431799129079809] Sending RoundStart to Player 10106021303 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_431799129079809] RoundStart: Player 90000080171 buff options: [104, 101, 109]
[AutoChessScene_431799129079809] RoundStart board data: player:90000080171 heroes:5
[AutoChessScene_431799129079809] RoundStart board data: player:90000075484 heroes:5
[AutoChessScene_431799129079809] Sending RoundStart to Player 90000080171 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_431799129079809] RoundStart: Player 90000075484 buff options: [109, 107, 110]
[AutoChessScene_431799129079809] RoundStart board data: player:90000080171 heroes:5
[AutoChessScene_431799129079809] RoundStart board data: player:90000075484 heroes:5
[AutoChessScene_431799129079809] Sending RoundStart to Player 90000075484 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_431799129079809] RoundStart: Player 90000042297 buff options: [104, 108, 101]
[AutoChessScene_431799129079809] RoundStart board data: player:90000042297 heroes:5
[AutoChessScene_431799129079809] RoundStart board data: player:10106021303 heroes:4
[AutoChessScene_431799129079809] Sending RoundStart to Player 90000042297 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_431799129079809] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 431799129079809 state to StateRoundStart
[AutoChessScene_431799129079809] State change sent to GameServer: StateRoundSettlement -> StateRoundStart (R2)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleStateManager_431799129079809] ===== ROUND 2 INITIALIZATION COMPLETE =====
[BattleStateManager_431799129079809] Buff selection timer started: 25000ms
[BattleStateManager_431799129079809] State: StateRoundStart -> StatePreparation (R2, 65000ms)
[AutoChessScene_431799129079809] Preparation phase started
[BuffManager_431799129079809] Added buff 104 (Buff_104) to player 90000080171
[AutoChessScene_431799129079809] Auto-selected buff 104 for bot player 90000080171
[CheckerBoard_431799129079809] Placed entity 11 at position (5, 3)
[CheckerBoard_431799129079809] Created entity ID:11 ConfigID:102 at (5, 3) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 12 at position (5, 6)
[CheckerBoard_431799129079809] Created entity ID:12 ConfigID:102 at (5, 6) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 13 at position (3, 5)
[CheckerBoard_431799129079809] Created entity ID:13 ConfigID:102 at (3, 5) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 14 at position (5, 2)
[CheckerBoard_431799129079809] Created entity ID:14 ConfigID:101 at (5, 2) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 15 at position (2, 6)
[CheckerBoard_431799129079809] Created entity ID:15 ConfigID:101 at (2, 6) for player 90000080171
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 90000080171: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 90000080171 in My area
[AutoChessScene_431799129079809] Generated 5 heroes for player 90000080171: 5 placed on board, 0 in temporary slots
[AutoChessScene_431799129079809] Generated 5 new heroes for bot player 90000080171 after buff selection
[BuffManager_431799129079809] Added buff 109 (Buff_109) to player 90000075484
[AutoChessScene_431799129079809] Auto-selected buff 109 for bot player 90000075484
[CheckerBoard_431799129079809] Placed entity 16 at position (8, 3)
[CheckerBoard_431799129079809] Created entity ID:16 ConfigID:103 at (8, 3) for player 90000075484
[CheckerBoard_431799129079809] Placed entity 17 at position (10, 5)
[CheckerBoard_431799129079809] Created entity ID:17 ConfigID:103 at (10, 5) for player 90000075484
[CheckerBoard_431799129079809] Placed entity 18 at position (8, 4)
[CheckerBoard_431799129079809] Created entity ID:18 ConfigID:101 at (8, 4) for player 90000075484
[CheckerBoard_431799129079809] Placed entity 19 at position (10, 3)
[CheckerBoard_431799129079809] Created entity ID:19 ConfigID:103 at (10, 3) for player 90000075484
[CheckerBoard_431799129079809] Placed entity 20 at position (8, 1)
[CheckerBoard_431799129079809] Created entity ID:20 ConfigID:103 at (8, 1) for player 90000075484
[CheckerBoard_431799129079809] CheckTimes limit (4) reached for 1 hero types for player 90000075484
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 90000075484: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 90000075484 in Enemy area
[AutoChessScene_431799129079809] Generated 5 heroes for player 90000075484: 5 placed on board, 0 in temporary slots
[AutoChessScene_431799129079809] Generated 5 new heroes for bot player 90000075484 after buff selection
[BuffManager_431799129079809] Added buff 104 (Buff_104) to player 90000042297
[AutoChessScene_431799129079809] Auto-selected buff 104 for bot player 90000042297
[CheckerBoard_431799129079809] Placed entity 10 at position (5, 1)
[CheckerBoard_431799129079809] Created entity ID:10 ConfigID:102 at (5, 1) for player 90000042297
[CheckerBoard_431799129079809] Placed entity 11 at position (2, 4)
[CheckerBoard_431799129079809] Created entity ID:11 ConfigID:102 at (2, 4) for player 90000042297
[CheckerBoard_431799129079809] Placed entity 12 at position (5, 6)
[CheckerBoard_431799129079809] Created entity ID:12 ConfigID:101 at (5, 6) for player 90000042297
[CheckerBoard_431799129079809] Placed entity 13 at position (5, 4)
[CheckerBoard_431799129079809] Created entity ID:13 ConfigID:102 at (5, 4) for player 90000042297
[CheckerBoard_431799129079809] Placed entity 14 at position (3, 3)
[CheckerBoard_431799129079809] Created entity ID:14 ConfigID:101 at (3, 3) for player 90000042297
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 90000042297: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 90000042297 in My area
[AutoChessScene_431799129079809] Generated 5 heroes for player 90000042297: 5 placed on board, 0 in temporary slots
[AutoChessScene_431799129079809] Generated 5 new heroes for bot player 90000042297 after buff selection
[PlayerManager_431799129079809] Player 90000080171 ready status set to True
[PlayerManager_431799129079809] Player 90000075484 ready status set to True
[PlayerManager_431799129079809] Player 90000042297 ready status set to True
[AutoChessScene_431799129079809] Auto-ready 3 additional bots
[AutoChessScene_431799129079809] Free operation phase started
[BattleService] Updated battle 431799129079809 state to StatePreparation
[AutoChessScene_431799129079809] State change sent to GameServer: StateRoundStart -> StatePreparation (R2)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED SelectBuffer RPC =====
[BattleService] Player 10106021303 is selecting buff 103
[BuffManager_431799129079809] Added buff 103 (Buff_103) to player 10106021303
[CheckerBoard_431799129079809] Placed entity 15 at position (8, 4)
[CheckerBoard_431799129079809] Created entity ID:15 ConfigID:103 at (8, 4) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 16 at position (9, 3)
[CheckerBoard_431799129079809] Created entity ID:16 ConfigID:102 at (9, 3) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 17 at position (10, 2)
[CheckerBoard_431799129079809] Created entity ID:17 ConfigID:101 at (10, 2) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 18 at position (9, 6)
[CheckerBoard_431799129079809] Created entity ID:18 ConfigID:103 at (9, 6) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 19 at position (9, 1)
[CheckerBoard_431799129079809] Created entity ID:19 ConfigID:102 at (9, 1) for player 10106021303
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 10106021303: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 10106021303 in Enemy area
[AutoChessScene_431799129079809] Generated 5 heroes for player 10106021303: 5 placed on board, 0 in temporary slots
[AutoChessScene_431799129079809] Player 10106021303 selected buff 103, generated 5 new heroes
[BattleService] SelectBuffer response: Code=0, NewHeroes=5
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_431799129079809] MergeHero operation: Player 10106021303, From GridID 51 → To GridID 49
[CheckerBoard_431799129079809] Merged entity from (9, 3) to (9, 1). Star level increased from 1 to 2
[AutoChessScene_431799129079809] MergeHero operation: Player 10106021303, From GridID 46 → To GridID 34
[CheckerBoard_431799129079809] Merged entity from (8, 4) to (6, 4). Star level increased from 1 to 2
[AutoChessScene_431799129079809] Processing 1 move operations before setting player 10106021303 ready
[CheckerBoard_431799129079809] Cannot move: target position (6, 4) is occupied by entity 4
[AutoChessScene_431799129079809] Player 10106021303 move operation: GridID(32->34) → Coord((6,2)->(6,4)), success: False
[AutoChessScene_431799129079809] Failed to execute move operation for player 10106021303: GridID(32->34) → Coord((6,2)->(6,4))
[PlayerManager_431799129079809] Player 10106021303 ready status set to True
[PlayerManager_431799129079809] All players are ready!
[AutoChessScene_431799129079809] All players are ready, transitioning to next state
[BattleStateManager_431799129079809] State: StatePreparation -> StateBattleStarting (R2, 1000ms)
[AutoChessScene_431799129079809] Applying battle start buffs for all players
[BuffManager_431799129079809] Applying battle start buff 103 (Buff_103) for player 10106021303
[BuffManager_431799129079809] Applying battle start buff 104 (Buff_104) for player 90000080171
[BuffManager_431799129079809] Applying battle start buff 109 (Buff_109) for player 90000075484
[BuffManager_431799129079809] Applying battle start buff 104 (Buff_104) for player 90000042297
[AutoChessScene_431799129079809] Camp info for player 90000042297: 10 heroes added
[AutoChessScene_431799129079809] Camp info for player 10106021303: 7 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 10106021303, Team order: [90000042297, 10106021303], total GridIDs used: 17
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 10106021303 vs Opponent 90000042297 with 2 teams
[AutoChessScene_431799129079809] Camp info for player 90000080171: 10 heroes added
[AutoChessScene_431799129079809] Camp info for player 90000075484: 10 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 90000080171, Team order: [90000080171, 90000075484], total GridIDs used: 20
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 90000080171 vs Opponent 90000075484 with 2 teams
[AutoChessScene_431799129079809] Camp info for player 90000080171: 10 heroes added
[AutoChessScene_431799129079809] Camp info for player 90000075484: 10 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 90000075484, Team order: [90000080171, 90000075484], total GridIDs used: 20
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 90000075484 vs Opponent 90000080171 with 2 teams
[AutoChessScene_431799129079809] Camp info for player 90000042297: 10 heroes added
[AutoChessScene_431799129079809] Camp info for player 10106021303: 7 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 90000042297, Team order: [90000042297, 10106021303], total GridIDs used: 17
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 90000042297 vs Opponent 10106021303 with 2 teams
[AutoChessScene_431799129079809] Sent RoundBattleStart notifications with seed: 1816907615
[BattleService] Updated battle 431799129079809 state to StateBattleStarting
[AutoChessScene_431799129079809] State change sent to GameServer: StatePreparation -> StateBattleStarting (R2)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[AutoChessScene_431799129079809] Player 10106021303 set ready status to True
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleStateManager_431799129079809] State: StateBattleStarting -> StateBattleInProgress (R2, 65000ms)
[AutoChessScene_431799129079809] Starting all battle instances
[BattleInstance] 431799129079809_1 battle started
[BattleInstance] 431799129079809_2 battle started
[BattleInstanceManager] Started all 2 battle instances
[AutoChessScene_431799129079809] Auto EndBattle for bot 90000080171 vs bot 90000075484, random result: bot 90000080171 wins = True
[AutoChessScene_431799129079809] Player 90000080171 sent EndBattleReq (win: True), instance: 431799129079809_1
[AutoChessScene_431799129079809] Waiting for opponent 90000075484 to send EndBattleReq for instance 431799129079809_1
[AutoChessScene_431799129079809] Player 90000075484 sent EndBattleReq (win: False), instance: 431799129079809_1
[BattleInstance] 431799129079809_1 battle finished, winner: 90000080171, loser: 90000075484
[AutoChessScene_431799129079809] Battle instance 431799129079809_1 completed: Winner 90000080171, Loser 90000075484
[AutoChessScene_431799129079809] Bot 90000042297 vs real player 10106021303, waiting for real player result
[AutoChessScene_431799129079809] Bot vs real player battles will be handled by system timeout (65s)
[BattleService] Updated battle 431799129079809 state to StateBattleInProgress
[AutoChessScene_431799129079809] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R2)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] EndBattle uid: 10106021303, win: True
[AutoChessScene_431799129079809] Player 10106021303 sent EndBattleReq (win: True), instance: 431799129079809_2
[AutoChessScene_431799129079809] Auto EndBattle for bot 90000042297 vs real player 10106021303, bot result: False
[BattleInstance] 431799129079809_2 battle finished, winner: 10106021303, loser: 90000042297
[AutoChessScene_431799129079809] Battle instance 431799129079809_2 completed: Winner 10106021303, Loser 90000042297
[AutoChessScene_431799129079809] All battle instances finished, proceeding to settlement
[BattleStateManager_431799129079809] State: StateBattleInProgress -> StateRoundSettlement (R2, 5000ms)
[AutoChessScene_431799129079809] Processing battle results
[PlayerManager_431799129079809] Player 90000075484 health reduced by 1, current health: 1
[AutoChessScene_431799129079809] Player 90000075484 lost 1 health, winner: 90000080171
[AutoChessScene_431799129079809] Sent RoundBattleEnd notification (isEnd=false) - Winner: 90000080171, Loser: 90000075484
[PlayerManager_431799129079809] Player 90000042297 health reduced by 1, current health: 1
[AutoChessScene_431799129079809] Player 90000042297 lost 1 health, winner: 10106021303
[AutoChessScene_431799129079809] Sent RoundBattleEnd notification (isEnd=false) - Winner: 10106021303, Loser: 90000042297
[AutoChessScene_431799129079809] Checking players elimination
[AutoChessScene_431799129079809] Active players remaining: 4
[AutoChessScene_431799129079809] Waiting for all players to confirm round settlement before starting new round
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000080171
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000075484
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000042297
[BattleService] Updated battle 431799129079809 state to StateRoundSettlement
[AutoChessScene_431799129079809] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R2)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10106021303 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10106021303 confirmed round settlement, current state: StateRoundSettlement
[AutoChessScene_431799129079809] Player 10106021303 confirmed round settlement, count: 4
[AutoChessScene_431799129079809] Real player 10106021303 confirmed, auto-confirming all bots
[AutoChessScene_431799129079809] All players confirmed round settlement, starting new round
[PlayerManager_431799129079809] Player 10106021303 ready status set to False
[PlayerManager_431799129079809] Player 90000080171 ready status set to False
[PlayerManager_431799129079809] Player 90000075484 ready status set to False
[PlayerManager_431799129079809] Player 90000042297 ready status set to False
[BattleStateManager_431799129079809] ===== STARTING NEW ROUND 3 =====
[BattleStateManager_431799129079809] Round 3 has buff selection: False
[BattleStateManager_431799129079809] Publishing RoundStartedEvent for round 3
[AutoChessScene_431799129079809] Round 3 started
[BattleStateManager_431799129079809] Setting state to StateRoundStart for round 3
[BattleStateManager_431799129079809] State: StateRoundSettlement -> StateRoundStart (R3, 1000ms)
[AutoChessScene_431799129079809] HandleRoundStart: 4 active players
[AutoChessScene_431799129079809] Valid player: 10106021303, Health: 3
[AutoChessScene_431799129079809] Valid player: 90000080171, Health: 3
[AutoChessScene_431799129079809] Valid player: 90000075484, Health: 1
[AutoChessScene_431799129079809] Valid player: 90000042297, Health: 1
[AutoChessScene_431799129079809] Player 10106021303 has 7 entities to save
[PlayerManager_431799129079809] Saved board data: player:10106021303 entities:7
[PlayerManager_431799129079809] Saved prev round data: player:10106021303 entities:7
[AutoChessScene_431799129079809] Player 90000080171 has 10 entities to save
[PlayerManager_431799129079809] Saved board data: player:90000080171 entities:10
[PlayerManager_431799129079809] Saved prev round data: player:90000080171 entities:10
[AutoChessScene_431799129079809] Player 90000075484 has 10 entities to save
[PlayerManager_431799129079809] Saved board data: player:90000075484 entities:10
[PlayerManager_431799129079809] Saved prev round data: player:90000075484 entities:10
[AutoChessScene_431799129079809] Player 90000042297 has 10 entities to save
[PlayerManager_431799129079809] Saved board data: player:90000042297 entities:10
[PlayerManager_431799129079809] Saved prev round data: player:90000042297 entities:10
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player 90000075484 vs Player 10106021303
[OpponentPairManager] Random pair: Player 90000080171 vs Player 90000042297
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_431799129079809] Created 4 opponent pairs
[PlayerManager_431799129079809] Set player opponents, count: 4
[BattleInstanceManager] Created instance 431799129079809_1 for active players 90000075484 vs 10106021303
[CheckerBoard_431799129079809] Cleared checkerboard
[CheckerBoard_431799129079809] Initialized
[BattleInstance] 431799129079809_1 created with players: 90000075484, 10106021303
[BattleInstanceManager] Created instance 431799129079809_2 for active players 90000080171 vs 90000042297
[CheckerBoard_431799129079809] Cleared checkerboard
[CheckerBoard_431799129079809] Initialized
[BattleInstance] 431799129079809_2 created with players: 90000080171, 90000042297
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_431799129079809] Restoring player 10106021303 to Enemy area (rows 6-10) based on current instance position
[CheckerBoard_431799129079809] Placed entity 1 at position (6, 1)
[CheckerBoard_431799129079809] Created entity ID:1 ConfigID:101 at (6, 1) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 1: (6,1)->(6,1), GridID:31->31
[CheckerBoard_431799129079809] Placed entity 2 at position (6, 2)
[CheckerBoard_431799129079809] Created entity ID:2 ConfigID:103 at (6, 2) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 2: (6,2)->(6,2), GridID:32->32
[CheckerBoard_431799129079809] Placed entity 3 at position (6, 3)
[CheckerBoard_431799129079809] Created entity ID:3 ConfigID:102 at (6, 3) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 3: (6,3)->(6,3), GridID:33->33
[CheckerBoard_431799129079809] Placed entity 4 at position (6, 4)
[CheckerBoard_431799129079809] Created entity ID:4 ConfigID:103 at (6, 4) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 4: (6,4)->(6,4), GridID:34->34
[CheckerBoard_431799129079809] Placed entity 5 at position (6, 5)
[CheckerBoard_431799129079809] Created entity ID:5 ConfigID:102 at (6, 5) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 5: (9,1)->(6,5), GridID:49->35
[CheckerBoard_431799129079809] Placed entity 6 at position (6, 6)
[CheckerBoard_431799129079809] Created entity ID:6 ConfigID:103 at (6, 6) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 6: (9,6)->(6,6), GridID:54->36
[CheckerBoard_431799129079809] Placed entity 7 at position (7, 1)
[CheckerBoard_431799129079809] Created entity ID:7 ConfigID:101 at (7, 1) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 7: (10,2)->(7,1), GridID:56->37
[AutoChessScene_431799129079809] Restored board: player:10106021303 entities:7/7
[AutoChessScene_431799129079809] Restoring player 90000080171 to My area (rows 1-5) based on current instance position
[CheckerBoard_431799129079809] Placed entity 1 at position (1, 1)
[CheckerBoard_431799129079809] Created entity ID:1 ConfigID:102 at (1, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 1: (1,1)->(1,1), GridID:1->1
[CheckerBoard_431799129079809] Placed entity 2 at position (1, 2)
[CheckerBoard_431799129079809] Created entity ID:2 ConfigID:101 at (1, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 2: (1,2)->(1,2), GridID:2->2
[CheckerBoard_431799129079809] Placed entity 3 at position (1, 3)
[CheckerBoard_431799129079809] Created entity ID:3 ConfigID:101 at (1, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 3: (1,3)->(1,3), GridID:3->3
[CheckerBoard_431799129079809] Placed entity 4 at position (1, 4)
[CheckerBoard_431799129079809] Created entity ID:4 ConfigID:101 at (1, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 4: (1,4)->(1,4), GridID:4->4
[CheckerBoard_431799129079809] Placed entity 5 at position (1, 5)
[CheckerBoard_431799129079809] Created entity ID:5 ConfigID:101 at (1, 5) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 5: (1,5)->(1,5), GridID:5->5
[CheckerBoard_431799129079809] Placed entity 6 at position (1, 6)
[CheckerBoard_431799129079809] Created entity ID:6 ConfigID:101 at (1, 6) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 6: (2,6)->(1,6), GridID:12->6
[CheckerBoard_431799129079809] Placed entity 7 at position (2, 1)
[CheckerBoard_431799129079809] Created entity ID:7 ConfigID:102 at (2, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 7: (3,5)->(2,1), GridID:17->7
[CheckerBoard_431799129079809] Placed entity 8 at position (2, 2)
[CheckerBoard_431799129079809] Created entity ID:8 ConfigID:101 at (2, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 8: (5,2)->(2,2), GridID:26->8
[CheckerBoard_431799129079809] Placed entity 9 at position (2, 3)
[CheckerBoard_431799129079809] Created entity ID:9 ConfigID:102 at (2, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 9: (5,3)->(2,3), GridID:27->9
[CheckerBoard_431799129079809] Placed entity 10 at position (2, 4)
[CheckerBoard_431799129079809] Created entity ID:10 ConfigID:102 at (2, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 10: (5,6)->(2,4), GridID:30->10
[AutoChessScene_431799129079809] Restored board: player:90000080171 entities:10/10
[AutoChessScene_431799129079809] Restoring player 90000075484 to My area (rows 1-5) based on current instance position
[CheckerBoard_431799129079809] Placed entity 8 at position (1, 1)
[CheckerBoard_431799129079809] Created entity ID:8 ConfigID:103 at (1, 1) for player 90000075484
[AutoChessScene_431799129079809] Restored entity 8: (6,1)->(1,1), GridID:31->1
[CheckerBoard_431799129079809] Placed entity 9 at position (1, 2)
[CheckerBoard_431799129079809] Created entity ID:9 ConfigID:102 at (1, 2) for player 90000075484
[AutoChessScene_431799129079809] Restored entity 9: (6,2)->(1,2), GridID:32->2
[CheckerBoard_431799129079809] Placed entity 10 at position (1, 3)
[CheckerBoard_431799129079809] Created entity ID:10 ConfigID:103 at (1, 3) for player 90000075484
[AutoChessScene_431799129079809] Restored entity 10: (6,3)->(1,3), GridID:33->3
[CheckerBoard_431799129079809] Placed entity 11 at position (1, 4)
[CheckerBoard_431799129079809] Created entity ID:11 ConfigID:103 at (1, 4) for player 90000075484
[AutoChessScene_431799129079809] Restored entity 11: (6,4)->(1,4), GridID:34->4
[CheckerBoard_431799129079809] Placed entity 12 at position (1, 5)
[CheckerBoard_431799129079809] Created entity ID:12 ConfigID:102 at (1, 5) for player 90000075484
[AutoChessScene_431799129079809] Restored entity 12: (6,5)->(1,5), GridID:35->5
[CheckerBoard_431799129079809] Placed entity 13 at position (1, 6)
[CheckerBoard_431799129079809] Created entity ID:13 ConfigID:103 at (1, 6) for player 90000075484
[AutoChessScene_431799129079809] Restored entity 13: (8,1)->(1,6), GridID:43->6
[CheckerBoard_431799129079809] Placed entity 14 at position (2, 1)
[CheckerBoard_431799129079809] Created entity ID:14 ConfigID:103 at (2, 1) for player 90000075484
[AutoChessScene_431799129079809] Restored entity 14: (8,3)->(2,1), GridID:45->7
[CheckerBoard_431799129079809] Placed entity 15 at position (2, 2)
[CheckerBoard_431799129079809] Created entity ID:15 ConfigID:101 at (2, 2) for player 90000075484
[AutoChessScene_431799129079809] Restored entity 15: (8,4)->(2,2), GridID:46->8
[CheckerBoard_431799129079809] Placed entity 16 at position (2, 3)
[CheckerBoard_431799129079809] Created entity ID:16 ConfigID:103 at (2, 3) for player 90000075484
[AutoChessScene_431799129079809] Restored entity 16: (10,3)->(2,3), GridID:57->9
[CheckerBoard_431799129079809] Placed entity 17 at position (2, 4)
[CheckerBoard_431799129079809] Created entity ID:17 ConfigID:103 at (2, 4) for player 90000075484
[AutoChessScene_431799129079809] Restored entity 17: (10,5)->(2,4), GridID:59->10
[AutoChessScene_431799129079809] Restored board: player:90000075484 entities:10/10
[AutoChessScene_431799129079809] Restoring player 90000042297 to Enemy area (rows 6-10) based on current instance position
[CheckerBoard_431799129079809] Placed entity 11 at position (6, 1)
[CheckerBoard_431799129079809] Created entity ID:11 ConfigID:101 at (6, 1) for player 90000042297
[AutoChessScene_431799129079809] Restored entity 11: (1,1)->(6,1), GridID:1->31
[CheckerBoard_431799129079809] Placed entity 12 at position (6, 2)
[CheckerBoard_431799129079809] Created entity ID:12 ConfigID:102 at (6, 2) for player 90000042297
[AutoChessScene_431799129079809] Restored entity 12: (1,2)->(6,2), GridID:2->32
[CheckerBoard_431799129079809] Placed entity 13 at position (6, 3)
[CheckerBoard_431799129079809] Created entity ID:13 ConfigID:101 at (6, 3) for player 90000042297
[AutoChessScene_431799129079809] Restored entity 13: (1,3)->(6,3), GridID:3->33
[CheckerBoard_431799129079809] Placed entity 14 at position (6, 4)
[CheckerBoard_431799129079809] Created entity ID:14 ConfigID:101 at (6, 4) for player 90000042297
[AutoChessScene_431799129079809] Restored entity 14: (1,4)->(6,4), GridID:4->34
[CheckerBoard_431799129079809] Placed entity 15 at position (6, 5)
[CheckerBoard_431799129079809] Created entity ID:15 ConfigID:103 at (6, 5) for player 90000042297
[AutoChessScene_431799129079809] Restored entity 15: (1,5)->(6,5), GridID:5->35
[CheckerBoard_431799129079809] Placed entity 16 at position (6, 6)
[CheckerBoard_431799129079809] Created entity ID:16 ConfigID:102 at (6, 6) for player 90000042297
[AutoChessScene_431799129079809] Restored entity 16: (2,4)->(6,6), GridID:10->36
[CheckerBoard_431799129079809] Placed entity 17 at position (7, 1)
[CheckerBoard_431799129079809] Created entity ID:17 ConfigID:101 at (7, 1) for player 90000042297
[AutoChessScene_431799129079809] Restored entity 17: (3,3)->(7,1), GridID:15->37
[CheckerBoard_431799129079809] Placed entity 18 at position (7, 2)
[CheckerBoard_431799129079809] Created entity ID:18 ConfigID:102 at (7, 2) for player 90000042297
[AutoChessScene_431799129079809] Restored entity 18: (5,1)->(7,2), GridID:25->38
[CheckerBoard_431799129079809] Placed entity 19 at position (7, 3)
[CheckerBoard_431799129079809] Created entity ID:19 ConfigID:102 at (7, 3) for player 90000042297
[AutoChessScene_431799129079809] Restored entity 19: (5,4)->(7,3), GridID:28->39
[CheckerBoard_431799129079809] Placed entity 20 at position (7, 4)
[CheckerBoard_431799129079809] Created entity ID:20 ConfigID:101 at (7, 4) for player 90000042297
[AutoChessScene_431799129079809] Restored entity 20: (5,6)->(7,4), GridID:30->40
[AutoChessScene_431799129079809] Restored board: player:90000042297 entities:10/10
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 10106021303
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 90000080171
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 90000075484
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 90000042297
[PlayerManager_431799129079809] Reset all players ready status
[AutoChessScene_431799129079809] Round started with 2 battle instances
[AutoChessScene_431799129079809] Generating 5 heroes for all players in round 3
[CheckerBoard_431799129079809] Placed entity 18 at position (8, 5)
[CheckerBoard_431799129079809] Created entity ID:18 ConfigID:102 at (8, 5) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 19 at position (10, 5)
[CheckerBoard_431799129079809] Created entity ID:19 ConfigID:102 at (10, 5) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 20 at position (8, 3)
[CheckerBoard_431799129079809] Created entity ID:20 ConfigID:103 at (8, 3) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 21 at position (9, 1)
[CheckerBoard_431799129079809] Created entity ID:21 ConfigID:102 at (9, 1) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 22 at position (7, 6)
[CheckerBoard_431799129079809] Created entity ID:22 ConfigID:102 at (7, 6) for player 10106021303
[CheckerBoard_431799129079809] CheckTimes limit (4) reached for 1 hero types for player 10106021303
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 10106021303: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 10106021303 in Enemy area
[AutoChessScene_431799129079809] Generated 5 heroes for player 10106021303: 5 placed on board, 0 in temporary slots
[CheckerBoard_431799129079809] Placed entity 21 at position (2, 5)
[CheckerBoard_431799129079809] Created entity ID:21 ConfigID:101 at (2, 5) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 22 at position (4, 4)
[CheckerBoard_431799129079809] Created entity ID:22 ConfigID:102 at (4, 4) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 23 at position (3, 4)
[CheckerBoard_431799129079809] Created entity ID:23 ConfigID:103 at (3, 4) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 24 at position (5, 6)
[CheckerBoard_431799129079809] Created entity ID:24 ConfigID:102 at (5, 6) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 25 at position (3, 6)
[CheckerBoard_431799129079809] Created entity ID:25 ConfigID:103 at (3, 6) for player 90000080171
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 90000080171: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 90000080171 in My area
[AutoChessScene_431799129079809] Generated 5 heroes for player 90000080171: 5 placed on board, 0 in temporary slots
[CheckerBoard_431799129079809] Placed entity 23 at position (4, 6)
[CheckerBoard_431799129079809] Created entity ID:23 ConfigID:103 at (4, 6) for player 90000075484
[CheckerBoard_431799129079809] Placed entity 24 at position (3, 6)
[CheckerBoard_431799129079809] Created entity ID:24 ConfigID:102 at (3, 6) for player 90000075484
[CheckerBoard_431799129079809] Placed entity 25 at position (4, 1)
[CheckerBoard_431799129079809] Created entity ID:25 ConfigID:103 at (4, 1) for player 90000075484
[CheckerBoard_431799129079809] Placed entity 26 at position (4, 5)
[CheckerBoard_431799129079809] Created entity ID:26 ConfigID:101 at (4, 5) for player 90000075484
[CheckerBoard_431799129079809] Placed entity 27 at position (4, 4)
[CheckerBoard_431799129079809] Created entity ID:27 ConfigID:103 at (4, 4) for player 90000075484
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 90000075484: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 90000075484 in My area
[AutoChessScene_431799129079809] Generated 5 heroes for player 90000075484: 5 placed on board, 0 in temporary slots
[CheckerBoard_431799129079809] Placed entity 26 at position (10, 5)
[CheckerBoard_431799129079809] Created entity ID:26 ConfigID:102 at (10, 5) for player 90000042297
[CheckerBoard_431799129079809] Placed entity 27 at position (8, 1)
[CheckerBoard_431799129079809] Created entity ID:27 ConfigID:101 at (8, 1) for player 90000042297
[CheckerBoard_431799129079809] Placed entity 28 at position (10, 1)
[CheckerBoard_431799129079809] Created entity ID:28 ConfigID:101 at (10, 1) for player 90000042297
[CheckerBoard_431799129079809] Placed entity 29 at position (7, 5)
[CheckerBoard_431799129079809] Created entity ID:29 ConfigID:101 at (7, 5) for player 90000042297
[CheckerBoard_431799129079809] Placed entity 30 at position (10, 4)
[CheckerBoard_431799129079809] Created entity ID:30 ConfigID:103 at (10, 4) for player 90000042297
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 90000042297: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 90000042297 in Enemy area
[AutoChessScene_431799129079809] Generated 5 heroes for player 90000042297: 5 placed on board, 0 in temporary slots
[AutoChessScene_431799129079809] Player status: Total=4, Active=4
[AutoChessScene_431799129079809] Player 10106021303: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431799129079809] Player 90000080171: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431799129079809] Player 90000075484: Eliminated=False, Health=1, HasInstance=True
[AutoChessScene_431799129079809] Player 90000042297: Eliminated=False, Health=1, HasInstance=True
[AutoChessScene_431799129079809] Sending RoundStart notifications to 4 active players...
[AutoChessScene_431799129079809] RoundStart board data: player:90000075484 heroes:15
[AutoChessScene_431799129079809] RoundStart board data: player:10106021303 heroes:12
[AutoChessScene_431799129079809] Sending RoundStart to Player 10106021303 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431799129079809] RoundStart board data: player:90000080171 heroes:15
[AutoChessScene_431799129079809] RoundStart board data: player:90000042297 heroes:15
[AutoChessScene_431799129079809] Sending RoundStart to Player 90000080171 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431799129079809] RoundStart board data: player:90000075484 heroes:15
[AutoChessScene_431799129079809] RoundStart board data: player:10106021303 heroes:12
[AutoChessScene_431799129079809] Sending RoundStart to Player 90000075484 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431799129079809] RoundStart board data: player:90000080171 heroes:15
[AutoChessScene_431799129079809] RoundStart board data: player:90000042297 heroes:15
[AutoChessScene_431799129079809] Sending RoundStart to Player 90000042297 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431799129079809] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 431799129079809 state to StateRoundStart
[AutoChessScene_431799129079809] State change sent to GameServer: StateRoundSettlement -> StateRoundStart (R3)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleStateManager_431799129079809] ===== ROUND 3 INITIALIZATION COMPLETE =====
[BattleStateManager_431799129079809] State: StateRoundStart -> StatePreparation (R3, 65000ms)
[AutoChessScene_431799129079809] Preparation phase started
[PlayerManager_431799129079809] Player 90000080171 ready status set to True
[PlayerManager_431799129079809] Player 90000075484 ready status set to True
[PlayerManager_431799129079809] Player 90000042297 ready status set to True
[AutoChessScene_431799129079809] Auto-ready 3 additional bots
[AutoChessScene_431799129079809] Free operation phase started
[BattleService] Updated battle 431799129079809 state to StatePreparation
[AutoChessScene_431799129079809] State change sent to GameServer: StateRoundStart -> StatePreparation (R3)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[AutoChessScene_431799129079809] MergeHero operation: Player 10106021303, From GridID 47 → To GridID 42
[CheckerBoard_431799129079809] Merged entity from (8, 5) to (7, 6). Star level increased from 1 to 2
[AutoChessScene_431799129079809] MergeHero operation: Player 10106021303, From GridID 59 → To GridID 49
[CheckerBoard_431799129079809] Merged entity from (10, 5) to (9, 1). Star level increased from 1 to 2
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_431799129079809] Processing 3 move operations before setting player 10106021303 ready
[CheckerBoard_431799129079809] Cannot move: target position (6, 4) is occupied by entity 4
[AutoChessScene_431799129079809] Player 10106021303 move operation: GridID(32->34) → Coord((6,2)->(6,4)), success: False
[AutoChessScene_431799129079809] Failed to execute move operation for player 10106021303: GridID(32->34) → Coord((6,2)->(6,4))
[CheckerBoard_431799129079809] Cannot move: target position (8, 3) is occupied by entity 20
[AutoChessScene_431799129079809] Player 10106021303 move operation: GridID(32->45) → Coord((6,2)->(8,3)), success: False
[AutoChessScene_431799129079809] Failed to execute move operation for player 10106021303: GridID(32->45) → Coord((6,2)->(8,3))
[CheckerBoard_431799129079809] Cannot move: target position (6, 4) is occupied by entity 4
[AutoChessScene_431799129079809] Player 10106021303 move operation: GridID(45->34) → Coord((8,3)->(6,4)), success: False
[AutoChessScene_431799129079809] Failed to execute move operation for player 10106021303: GridID(45->34) → Coord((8,3)->(6,4))
[PlayerManager_431799129079809] Player 10106021303 ready status set to True
[PlayerManager_431799129079809] All players are ready!
[AutoChessScene_431799129079809] All players are ready, transitioning to next state
[BattleStateManager_431799129079809] State: StatePreparation -> StateBattleStarting (R3, 1000ms)
[AutoChessScene_431799129079809] Applying battle start buffs for all players
[BuffManager_431799129079809] Applying battle start buff 103 (Buff_103) for player 10106021303
[BuffManager_431799129079809] Applying battle start buff 104 (Buff_104) for player 90000080171
[BuffManager_431799129079809] Applying battle start buff 109 (Buff_109) for player 90000075484
[BuffManager_431799129079809] Applying battle start buff 104 (Buff_104) for player 90000042297
[AutoChessScene_431799129079809] Camp info for player 90000075484: 15 heroes added
[AutoChessScene_431799129079809] Camp info for player 10106021303: 10 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 10106021303, Team order: [90000075484, 10106021303], total GridIDs used: 25
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 10106021303 vs Opponent 90000075484 with 2 teams
[AutoChessScene_431799129079809] Camp info for player 90000080171: 15 heroes added
[AutoChessScene_431799129079809] Camp info for player 90000042297: 15 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 90000080171, Team order: [90000080171, 90000042297], total GridIDs used: 30
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 90000080171 vs Opponent 90000042297 with 2 teams
[AutoChessScene_431799129079809] Camp info for player 90000075484: 15 heroes added
[AutoChessScene_431799129079809] Camp info for player 10106021303: 10 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 90000075484, Team order: [90000075484, 10106021303], total GridIDs used: 25
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 90000075484 vs Opponent 10106021303 with 2 teams
[AutoChessScene_431799129079809] Camp info for player 90000080171: 15 heroes added
[AutoChessScene_431799129079809] Camp info for player 90000042297: 15 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 90000042297, Team order: [90000080171, 90000042297], total GridIDs used: 30
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 90000042297 vs Opponent 90000080171 with 2 teams
[AutoChessScene_431799129079809] Sent RoundBattleStart notifications with seed: 844279630
[BattleService] Updated battle 431799129079809 state to StateBattleStarting
[AutoChessScene_431799129079809] State change sent to GameServer: StatePreparation -> StateBattleStarting (R3)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[AutoChessScene_431799129079809] Player 10106021303 set ready status to True
[BattleStateManager_431799129079809] State: StateBattleStarting -> StateBattleInProgress (R3, 65000ms)
[AutoChessScene_431799129079809] Starting all battle instances
[BattleInstance] 431799129079809_1 battle started
[BattleInstance] 431799129079809_2 battle started
[BattleInstanceManager] Started all 2 battle instances
[AutoChessScene_431799129079809] Auto EndBattle for bot 90000080171 vs bot 90000042297, random result: bot 90000080171 wins = True
[AutoChessScene_431799129079809] Player 90000080171 sent EndBattleReq (win: True), instance: 431799129079809_2
[AutoChessScene_431799129079809] Waiting for opponent 90000042297 to send EndBattleReq for instance 431799129079809_2
[AutoChessScene_431799129079809] Player 90000042297 sent EndBattleReq (win: False), instance: 431799129079809_2
[BattleInstance] 431799129079809_2 battle finished, winner: 90000080171, loser: 90000042297
[AutoChessScene_431799129079809] Battle instance 431799129079809_2 completed: Winner 90000080171, Loser 90000042297
[AutoChessScene_431799129079809] Bot 90000075484 vs real player 10106021303, waiting for real player result
[AutoChessScene_431799129079809] Bot vs real player battles will be handled by system timeout (65s)
[BattleService] Updated battle 431799129079809 state to StateBattleInProgress
[AutoChessScene_431799129079809] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R3)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleService] EndBattle uid: 10106021303, win: True
[AutoChessScene_431799129079809] Player 10106021303 sent EndBattleReq (win: True), instance: 431799129079809_1
[AutoChessScene_431799129079809] Auto EndBattle for bot 90000075484 vs real player 10106021303, bot result: False
[BattleInstance] 431799129079809_1 battle finished, winner: 10106021303, loser: 90000075484
[AutoChessScene_431799129079809] Battle instance 431799129079809_1 completed: Winner 10106021303, Loser 90000075484
[AutoChessScene_431799129079809] All battle instances finished, proceeding to settlement
[BattleStateManager_431799129079809] State: StateBattleInProgress -> StateRoundSettlement (R3, 5000ms)
[AutoChessScene_431799129079809] Processing battle results
[PlayerManager_431799129079809] Player 90000075484 health reduced by 1, current health: 0
[PlayerManager_431799129079809] Player 90000075484 eliminated from battle 431799129079809
[AutoChessScene_431799129079809] Player 90000075484 has been eliminated
[AutoChessScene_431799129079809] Cleared 0 entities for player 90000075484
[AutoChessScene_431799129079809] Player 90000075484 lost 1 health, winner: 10106021303
[AutoChessScene_431799129079809] Sent RoundBattleEnd notification (isEnd=false) - Winner: 10106021303, Loser: 90000075484
[PlayerManager_431799129079809] Player 90000042297 health reduced by 1, current health: 0
[PlayerManager_431799129079809] Player 90000042297 eliminated from battle 431799129079809
[AutoChessScene_431799129079809] Player 90000042297 has been eliminated
[AutoChessScene_431799129079809] Cleared 0 entities for player 90000042297
[AutoChessScene_431799129079809] Player 90000042297 lost 1 health, winner: 90000080171
[AutoChessScene_431799129079809] Sent RoundBattleEnd notification (isEnd=false) - Winner: 90000080171, Loser: 90000042297
[AutoChessScene_431799129079809] Checking players elimination
[AutoChessScene_431799129079809] Active players remaining: 2
[AutoChessScene_431799129079809] Waiting for all players to confirm round settlement before starting new round
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000080171
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000075484
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000042297
[BattleService] Updated battle 431799129079809 state to StateRoundSettlement
[AutoChessScene_431799129079809] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R3)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10106021303 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10106021303 confirmed round settlement, current state: StateRoundSettlement
[AutoChessScene_431799129079809] Player 10106021303 confirmed round settlement, count: 4
[AutoChessScene_431799129079809] Real player 10106021303 confirmed, auto-confirming all bots
[AutoChessScene_431799129079809] All players confirmed round settlement, starting new round
[PlayerManager_431799129079809] Player 10106021303 ready status set to False
[PlayerManager_431799129079809] Player 90000080171 ready status set to False
[BattleStateManager_431799129079809] ===== STARTING NEW ROUND 4 =====
[BattleStateManager_431799129079809] Round 4 has buff selection: True
[BattleStateManager_431799129079809] Publishing RoundStartedEvent for round 4
[AutoChessScene_431799129079809] Round 4 started
[BattleStateManager_431799129079809] Setting state to StateRoundStart for round 4
[BattleStateManager_431799129079809] State: StateRoundSettlement -> StateRoundStart (R4, 1000ms)
[AutoChessScene_431799129079809] HandleRoundStart: 2 active players
[AutoChessScene_431799129079809] Valid player: 10106021303, Health: 3
[AutoChessScene_431799129079809] Valid player: 90000080171, Health: 3
[AutoChessScene_431799129079809] Player 10106021303 has 10 entities to save
[PlayerManager_431799129079809] Saved board data: player:10106021303 entities:10
[PlayerManager_431799129079809] Saved prev round data: player:10106021303 entities:10
[AutoChessScene_431799129079809] Player 90000080171 has 15 entities to save
[PlayerManager_431799129079809] Saved board data: player:90000080171 entities:15
[PlayerManager_431799129079809] Saved prev round data: player:90000080171 entities:15
[OpponentPairManager] Generating opponent pairs for round 1, active players: 2, eliminated: 0
[OpponentPairManager] Random pair: Player 90000080171 vs Player 10106021303
[OpponentPairManager] Generated 1 opponent pairs for round 1
[AutoChessScene_431799129079809] Created 2 opponent pairs
[PlayerManager_431799129079809] Set player opponents, count: 2
[BattleInstanceManager] Created instance 431799129079809_1 for active players 90000080171 vs 10106021303
[CheckerBoard_431799129079809] Cleared checkerboard
[CheckerBoard_431799129079809] Initialized
[BattleInstance] 431799129079809_1 created with players: 90000080171, 10106021303
[BattleInstanceManager] Created 1 battle instances for 2 players
[AutoChessScene_431799129079809] Restoring player 10106021303 to Enemy area (rows 6-10) based on current instance position
[CheckerBoard_431799129079809] Placed entity 1 at position (6, 1)
[CheckerBoard_431799129079809] Created entity ID:1 ConfigID:101 at (6, 1) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 1: (6,1)->(6,1), GridID:31->31
[CheckerBoard_431799129079809] Placed entity 2 at position (6, 2)
[CheckerBoard_431799129079809] Created entity ID:2 ConfigID:103 at (6, 2) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 2: (6,2)->(6,2), GridID:32->32
[CheckerBoard_431799129079809] Placed entity 3 at position (6, 3)
[CheckerBoard_431799129079809] Created entity ID:3 ConfigID:102 at (6, 3) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 3: (6,3)->(6,3), GridID:33->33
[CheckerBoard_431799129079809] Placed entity 4 at position (6, 4)
[CheckerBoard_431799129079809] Created entity ID:4 ConfigID:103 at (6, 4) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 4: (6,4)->(6,4), GridID:34->34
[CheckerBoard_431799129079809] Placed entity 5 at position (6, 5)
[CheckerBoard_431799129079809] Created entity ID:5 ConfigID:102 at (6, 5) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 5: (6,5)->(6,5), GridID:35->35
[CheckerBoard_431799129079809] Placed entity 6 at position (6, 6)
[CheckerBoard_431799129079809] Created entity ID:6 ConfigID:103 at (6, 6) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 6: (6,6)->(6,6), GridID:36->36
[CheckerBoard_431799129079809] Placed entity 7 at position (7, 1)
[CheckerBoard_431799129079809] Created entity ID:7 ConfigID:101 at (7, 1) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 7: (7,1)->(7,1), GridID:37->37
[CheckerBoard_431799129079809] Placed entity 8 at position (7, 2)
[CheckerBoard_431799129079809] Created entity ID:8 ConfigID:102 at (7, 2) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 8: (7,6)->(7,2), GridID:42->38
[CheckerBoard_431799129079809] Placed entity 9 at position (7, 3)
[CheckerBoard_431799129079809] Created entity ID:9 ConfigID:103 at (7, 3) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 9: (8,3)->(7,3), GridID:45->39
[CheckerBoard_431799129079809] Placed entity 10 at position (7, 4)
[CheckerBoard_431799129079809] Created entity ID:10 ConfigID:102 at (7, 4) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 10: (9,1)->(7,4), GridID:49->40
[AutoChessScene_431799129079809] Restored board: player:10106021303 entities:10/10
[AutoChessScene_431799129079809] Restoring player 90000080171 to My area (rows 1-5) based on current instance position
[CheckerBoard_431799129079809] Placed entity 11 at position (1, 1)
[CheckerBoard_431799129079809] Created entity ID:11 ConfigID:102 at (1, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 11: (1,1)->(1,1), GridID:1->1
[CheckerBoard_431799129079809] Placed entity 12 at position (1, 2)
[CheckerBoard_431799129079809] Created entity ID:12 ConfigID:101 at (1, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 12: (1,2)->(1,2), GridID:2->2
[CheckerBoard_431799129079809] Placed entity 13 at position (1, 3)
[CheckerBoard_431799129079809] Created entity ID:13 ConfigID:101 at (1, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 13: (1,3)->(1,3), GridID:3->3
[CheckerBoard_431799129079809] Placed entity 14 at position (1, 4)
[CheckerBoard_431799129079809] Created entity ID:14 ConfigID:101 at (1, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 14: (1,4)->(1,4), GridID:4->4
[CheckerBoard_431799129079809] Placed entity 15 at position (1, 5)
[CheckerBoard_431799129079809] Created entity ID:15 ConfigID:101 at (1, 5) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 15: (1,5)->(1,5), GridID:5->5
[CheckerBoard_431799129079809] Placed entity 16 at position (1, 6)
[CheckerBoard_431799129079809] Created entity ID:16 ConfigID:101 at (1, 6) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 16: (1,6)->(1,6), GridID:6->6
[CheckerBoard_431799129079809] Placed entity 17 at position (2, 1)
[CheckerBoard_431799129079809] Created entity ID:17 ConfigID:102 at (2, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 17: (2,1)->(2,1), GridID:7->7
[CheckerBoard_431799129079809] Placed entity 18 at position (2, 2)
[CheckerBoard_431799129079809] Created entity ID:18 ConfigID:101 at (2, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 18: (2,2)->(2,2), GridID:8->8
[CheckerBoard_431799129079809] Placed entity 19 at position (2, 3)
[CheckerBoard_431799129079809] Created entity ID:19 ConfigID:102 at (2, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 19: (2,3)->(2,3), GridID:9->9
[CheckerBoard_431799129079809] Placed entity 20 at position (2, 4)
[CheckerBoard_431799129079809] Created entity ID:20 ConfigID:102 at (2, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 20: (2,4)->(2,4), GridID:10->10
[CheckerBoard_431799129079809] Placed entity 21 at position (2, 5)
[CheckerBoard_431799129079809] Created entity ID:21 ConfigID:101 at (2, 5) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 21: (2,5)->(2,5), GridID:11->11
[CheckerBoard_431799129079809] Placed entity 22 at position (2, 6)
[CheckerBoard_431799129079809] Created entity ID:22 ConfigID:103 at (2, 6) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 22: (3,4)->(2,6), GridID:16->12
[CheckerBoard_431799129079809] Placed entity 23 at position (3, 1)
[CheckerBoard_431799129079809] Created entity ID:23 ConfigID:103 at (3, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 23: (3,6)->(3,1), GridID:18->13
[CheckerBoard_431799129079809] Placed entity 24 at position (3, 2)
[CheckerBoard_431799129079809] Created entity ID:24 ConfigID:102 at (3, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 24: (4,4)->(3,2), GridID:22->14
[CheckerBoard_431799129079809] Placed entity 25 at position (3, 3)
[CheckerBoard_431799129079809] Created entity ID:25 ConfigID:102 at (3, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 25: (5,6)->(3,3), GridID:30->15
[AutoChessScene_431799129079809] Restored board: player:90000080171 entities:15/15
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 10106021303
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 90000080171
[PlayerManager_431799129079809] Reset all players ready status
[AutoChessScene_431799129079809] Round started with 1 battle instances
[AutoChessScene_431799129079809] Generating buff options for all players
[BuffManager_431799129079809] Generated 3 buff options for player 10106021303: [106, 109, 104]
[AutoChessScene_431799129079809] Generated 3 buff options for player 10106021303: [106, 109, 104]
[BuffManager_431799129079809] Generated 3 buff options for player 90000080171: [110, 107, 106]
[AutoChessScene_431799129079809] Generated 3 buff options for player 90000080171: [110, 107, 106]
[AutoChessScene_431799129079809] Player status: Total=4, Active=2
[AutoChessScene_431799129079809] Player 10106021303: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431799129079809] Player 90000080171: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431799129079809] Player 90000075484: Eliminated=True, Health=0, HasInstance=False
[AutoChessScene_431799129079809] Player 90000042297: Eliminated=True, Health=0, HasInstance=False
[AutoChessScene_431799129079809] Sending RoundStart notifications to 2 active players...
[AutoChessScene_431799129079809] RoundStart: Player 10106021303 buff options: [106, 109, 104]
[AutoChessScene_431799129079809] RoundStart board data: player:90000080171 heroes:15
[AutoChessScene_431799129079809] RoundStart board data: player:10106021303 heroes:10
[AutoChessScene_431799129079809] Sending RoundStart to Player 10106021303 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_431799129079809] RoundStart: Player 90000080171 buff options: [110, 107, 106]
[AutoChessScene_431799129079809] RoundStart board data: player:90000080171 heroes:15
[AutoChessScene_431799129079809] RoundStart board data: player:10106021303 heroes:10
[AutoChessScene_431799129079809] Sending RoundStart to Player 90000080171 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_431799129079809] Successfully sent RoundStart notifications to all 2 players via NATS
[BattleService] Updated battle 431799129079809 state to StateRoundStart
[AutoChessScene_431799129079809] State change sent to GameServer: StateRoundSettlement -> StateRoundStart (R4)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleStateManager_431799129079809] ===== ROUND 4 INITIALIZATION COMPLETE =====
[BattleStateManager_431799129079809] Buff selection timer started: 25000ms
[BattleStateManager_431799129079809] State: StateRoundStart -> StatePreparation (R4, 65000ms)
[AutoChessScene_431799129079809] Preparation phase started
[BuffManager_431799129079809] Added buff 110 (Buff_110) to player 90000080171
[AutoChessScene_431799129079809] Auto-selected buff 110 for bot player 90000080171
[CheckerBoard_431799129079809] Placed entity 26 at position (5, 3)
[CheckerBoard_431799129079809] Created entity ID:26 ConfigID:102 at (5, 3) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 27 at position (5, 5)
[CheckerBoard_431799129079809] Created entity ID:27 ConfigID:102 at (5, 5) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 28 at position (4, 1)
[CheckerBoard_431799129079809] Created entity ID:28 ConfigID:103 at (4, 1) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 29 at position (4, 5)
[CheckerBoard_431799129079809] Created entity ID:29 ConfigID:101 at (4, 5) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 30 at position (4, 4)
[CheckerBoard_431799129079809] Created entity ID:30 ConfigID:102 at (4, 4) for player 90000080171
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 90000080171: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 90000080171 in My area
[AutoChessScene_431799129079809] Generated 5 heroes for player 90000080171: 5 placed on board, 0 in temporary slots
[AutoChessScene_431799129079809] Generated 5 new heroes for bot player 90000080171 after buff selection
[AutoChessScene_431799129079809] No buff options available for bot player 90000075484
[AutoChessScene_431799129079809] No buff options available for bot player 90000042297
[PlayerManager_431799129079809] Player 90000080171 ready status set to True
[AutoChessScene_431799129079809] Auto-ready 1 additional bots
[AutoChessScene_431799129079809] Free operation phase started
[BattleService] Updated battle 431799129079809 state to StatePreparation
[AutoChessScene_431799129079809] State change sent to GameServer: StateRoundStart -> StatePreparation (R4)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED SelectBuffer RPC =====
[BattleService] Player 10106021303 is selecting buff 109
[BuffManager_431799129079809] Added buff 109 (Buff_109) to player 10106021303
[CheckerBoard_431799129079809] Placed entity 31 at position (9, 2)
[CheckerBoard_431799129079809] Created entity ID:31 ConfigID:103 at (9, 2) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 32 at position (7, 6)
[CheckerBoard_431799129079809] Created entity ID:32 ConfigID:102 at (7, 6) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 33 at position (9, 1)
[CheckerBoard_431799129079809] Created entity ID:33 ConfigID:103 at (9, 1) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 34 at position (10, 2)
[CheckerBoard_431799129079809] Created entity ID:34 ConfigID:101 at (10, 2) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 35 at position (10, 1)
[CheckerBoard_431799129079809] Created entity ID:35 ConfigID:102 at (10, 1) for player 10106021303
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 10106021303: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 10106021303 in Enemy area
[AutoChessScene_431799129079809] Generated 5 heroes for player 10106021303: 5 placed on board, 0 in temporary slots
[AutoChessScene_431799129079809] Player 10106021303 selected buff 109, generated 5 new heroes
[BattleService] SelectBuffer response: Code=0, NewHeroes=5
[AutoChessScene_431799129079809] MergeHero operation: Player 10106021303, From GridID 38 → To GridID 40
[CheckerBoard_431799129079809] Merged entity from (7, 2) to (7, 4). Star level increased from 2 to 3
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_431799129079809] MergeHero operation: Player 10106021303, From GridID 35 → To GridID 55
[BattleInstance] 431799129079809_1 Processing 3 move operations before merge for player 10106021303
[CheckerBoard_431799129079809] Cannot move: target position (6, 3) is occupied by entity 3
[BattleInstance] 431799129079809_1 Move operation: 40 -> 33, success: False
[CheckerBoard_431799129079809] Cannot move: target position (6, 5) is occupied by entity 5
[BattleInstance] 431799129079809_1 Move operation: 33 -> 35, success: False
[CheckerBoard_431799129079809] Cannot move: target position (6, 5) is occupied by entity 5
[BattleInstance] 431799129079809_1 Move operation: 42 -> 35, success: False
[CheckerBoard_431799129079809] Swapped entities between (6, 5) and (10, 1)
[PlayerManager_431799129079809] Player 10106021303 ready status set to True
[PlayerManager_431799129079809] All players are ready!
[AutoChessScene_431799129079809] All players are ready, transitioning to next state
[BattleStateManager_431799129079809] State: StatePreparation -> StateBattleStarting (R4, 1000ms)
[AutoChessScene_431799129079809] Applying battle start buffs for all players
[BuffManager_431799129079809] Applying battle start buff 103 (Buff_103) for player 10106021303
[BuffManager_431799129079809] Applying battle start buff 109 (Buff_109) for player 10106021303
[BuffManager_431799129079809] Applying battle start buff 104 (Buff_104) for player 90000080171
[BuffManager_431799129079809] Applying battle start buff 110 (Buff_110) for player 90000080171
[AutoChessScene_431799129079809] Camp info for player 90000080171: 20 heroes added
[AutoChessScene_431799129079809] Camp info for player 10106021303: 14 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 10106021303, Team order: [90000080171, 10106021303], total GridIDs used: 34
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 10106021303 vs Opponent 90000080171 with 2 teams
[AutoChessScene_431799129079809] Camp info for player 90000080171: 20 heroes added
[AutoChessScene_431799129079809] Camp info for player 10106021303: 14 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 90000080171, Team order: [90000080171, 10106021303], total GridIDs used: 34
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 90000080171 vs Opponent 10106021303 with 2 teams
[AutoChessScene_431799129079809] Sent RoundBattleStart notifications with seed: 1315742630
[BattleService] Updated battle 431799129079809 state to StateBattleStarting
[AutoChessScene_431799129079809] State change sent to GameServer: StatePreparation -> StateBattleStarting (R4)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[AutoChessScene_431799129079809] Player 10106021303 set ready status to True
[BattleStateManager_431799129079809] State: StateBattleStarting -> StateBattleInProgress (R4, 65000ms)
[AutoChessScene_431799129079809] Starting all battle instances
[BattleInstance] 431799129079809_1 battle started
[BattleInstanceManager] Started all 1 battle instances
[AutoChessScene_431799129079809] Bot 90000080171 vs real player 10106021303, waiting for real player result
[AutoChessScene_431799129079809] Bot vs real player battles will be handled by system timeout (65s)
[BattleService] Updated battle 431799129079809 state to StateBattleInProgress
[AutoChessScene_431799129079809] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R4)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] EndBattle uid: 10106021303, win: True
[AutoChessScene_431799129079809] Player 10106021303 sent EndBattleReq (win: True), instance: 431799129079809_1
[AutoChessScene_431799129079809] Auto EndBattle for bot 90000080171 vs real player 10106021303, bot result: False
[BattleInstance] 431799129079809_1 battle finished, winner: 10106021303, loser: 90000080171
[AutoChessScene_431799129079809] Battle instance 431799129079809_1 completed: Winner 10106021303, Loser 90000080171
[AutoChessScene_431799129079809] All battle instances finished, proceeding to settlement
[BattleStateManager_431799129079809] State: StateBattleInProgress -> StateRoundSettlement (R4, 5000ms)
[AutoChessScene_431799129079809] Processing battle results
[PlayerManager_431799129079809] Player 90000080171 health reduced by 1, current health: 2
[AutoChessScene_431799129079809] Player 90000080171 lost 1 health, winner: 10106021303
[AutoChessScene_431799129079809] Sent RoundBattleEnd notification (isEnd=false) - Winner: 10106021303, Loser: 90000080171
[AutoChessScene_431799129079809] Checking players elimination
[AutoChessScene_431799129079809] Active players remaining: 2
[AutoChessScene_431799129079809] Waiting for all players to confirm round settlement before starting new round
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000080171
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000075484
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000042297
[BattleService] Updated battle 431799129079809 state to StateRoundSettlement
[AutoChessScene_431799129079809] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R4)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10106021303 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10106021303 confirmed round settlement, current state: StateRoundSettlement
[AutoChessScene_431799129079809] Player 10106021303 confirmed round settlement, count: 4
[AutoChessScene_431799129079809] Real player 10106021303 confirmed, auto-confirming all bots
[AutoChessScene_431799129079809] All players confirmed round settlement, starting new round
[PlayerManager_431799129079809] Player 10106021303 ready status set to False
[PlayerManager_431799129079809] Player 90000080171 ready status set to False
[BattleStateManager_431799129079809] ===== STARTING NEW ROUND 5 =====
[BattleStateManager_431799129079809] Round 5 has buff selection: False
[BattleStateManager_431799129079809] Publishing RoundStartedEvent for round 5
[AutoChessScene_431799129079809] Round 5 started
[BattleStateManager_431799129079809] Setting state to StateRoundStart for round 5
[BattleStateManager_431799129079809] State: StateRoundSettlement -> StateRoundStart (R5, 1000ms)
[AutoChessScene_431799129079809] HandleRoundStart: 2 active players
[AutoChessScene_431799129079809] Valid player: 10106021303, Health: 3
[AutoChessScene_431799129079809] Valid player: 90000080171, Health: 2
[AutoChessScene_431799129079809] Player 10106021303 has 14 entities to save
[PlayerManager_431799129079809] Saved board data: player:10106021303 entities:14
[PlayerManager_431799129079809] Saved prev round data: player:10106021303 entities:14
[AutoChessScene_431799129079809] Player 90000080171 has 20 entities to save
[PlayerManager_431799129079809] Saved board data: player:90000080171 entities:20
[PlayerManager_431799129079809] Saved prev round data: player:90000080171 entities:20
[OpponentPairManager] Generating opponent pairs for round 1, active players: 2, eliminated: 0
[OpponentPairManager] Random pair: Player 10106021303 vs Player 90000080171
[OpponentPairManager] Generated 1 opponent pairs for round 1
[AutoChessScene_431799129079809] Created 2 opponent pairs
[PlayerManager_431799129079809] Set player opponents, count: 2
[BattleInstanceManager] Created instance 431799129079809_1 for active players 10106021303 vs 90000080171
[CheckerBoard_431799129079809] Cleared checkerboard
[CheckerBoard_431799129079809] Initialized
[BattleInstance] 431799129079809_1 created with players: 10106021303, 90000080171
[BattleInstanceManager] Created 1 battle instances for 2 players
[AutoChessScene_431799129079809] Restoring player 10106021303 to My area (rows 1-5) based on current instance position
[CheckerBoard_431799129079809] Placed entity 1 at position (1, 1)
[CheckerBoard_431799129079809] Created entity ID:1 ConfigID:101 at (1, 1) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 1: (6,1)->(1,1), GridID:31->1
[CheckerBoard_431799129079809] Placed entity 2 at position (1, 2)
[CheckerBoard_431799129079809] Created entity ID:2 ConfigID:103 at (1, 2) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 2: (6,2)->(1,2), GridID:32->2
[CheckerBoard_431799129079809] Placed entity 3 at position (1, 3)
[CheckerBoard_431799129079809] Created entity ID:3 ConfigID:102 at (1, 3) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 3: (6,3)->(1,3), GridID:33->3
[CheckerBoard_431799129079809] Placed entity 4 at position (1, 4)
[CheckerBoard_431799129079809] Created entity ID:4 ConfigID:103 at (1, 4) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 4: (6,4)->(1,4), GridID:34->4
[CheckerBoard_431799129079809] Placed entity 5 at position (1, 5)
[CheckerBoard_431799129079809] Created entity ID:5 ConfigID:102 at (1, 5) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 5: (6,5)->(1,5), GridID:35->5
[CheckerBoard_431799129079809] Placed entity 6 at position (1, 6)
[CheckerBoard_431799129079809] Created entity ID:6 ConfigID:103 at (1, 6) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 6: (6,6)->(1,6), GridID:36->6
[CheckerBoard_431799129079809] Placed entity 7 at position (2, 1)
[CheckerBoard_431799129079809] Created entity ID:7 ConfigID:101 at (2, 1) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 7: (7,1)->(2,1), GridID:37->7
[CheckerBoard_431799129079809] Placed entity 8 at position (2, 2)
[CheckerBoard_431799129079809] Created entity ID:8 ConfigID:103 at (2, 2) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 8: (7,3)->(2,2), GridID:39->8
[CheckerBoard_431799129079809] Placed entity 9 at position (2, 3)
[CheckerBoard_431799129079809] Created entity ID:9 ConfigID:102 at (2, 3) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 9: (7,4)->(2,3), GridID:40->9
[CheckerBoard_431799129079809] Placed entity 10 at position (2, 4)
[CheckerBoard_431799129079809] Created entity ID:10 ConfigID:102 at (2, 4) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 10: (7,6)->(2,4), GridID:42->10
[CheckerBoard_431799129079809] Placed entity 11 at position (2, 5)
[CheckerBoard_431799129079809] Created entity ID:11 ConfigID:103 at (2, 5) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 11: (9,1)->(2,5), GridID:49->11
[CheckerBoard_431799129079809] Placed entity 12 at position (2, 6)
[CheckerBoard_431799129079809] Created entity ID:12 ConfigID:103 at (2, 6) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 12: (9,2)->(2,6), GridID:50->12
[CheckerBoard_431799129079809] Placed entity 13 at position (3, 1)
[CheckerBoard_431799129079809] Created entity ID:13 ConfigID:102 at (3, 1) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 13: (10,1)->(3,1), GridID:55->13
[CheckerBoard_431799129079809] Placed entity 14 at position (3, 2)
[CheckerBoard_431799129079809] Created entity ID:14 ConfigID:101 at (3, 2) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 14: (10,2)->(3,2), GridID:56->14
[AutoChessScene_431799129079809] Restored board: player:10106021303 entities:14/14
[AutoChessScene_431799129079809] Restoring player 90000080171 to Enemy area (rows 6-10) based on current instance position
[CheckerBoard_431799129079809] Placed entity 15 at position (6, 1)
[CheckerBoard_431799129079809] Created entity ID:15 ConfigID:102 at (6, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 15: (1,1)->(6,1), GridID:1->31
[CheckerBoard_431799129079809] Placed entity 16 at position (6, 2)
[CheckerBoard_431799129079809] Created entity ID:16 ConfigID:101 at (6, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 16: (1,2)->(6,2), GridID:2->32
[CheckerBoard_431799129079809] Placed entity 17 at position (6, 3)
[CheckerBoard_431799129079809] Created entity ID:17 ConfigID:101 at (6, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 17: (1,3)->(6,3), GridID:3->33
[CheckerBoard_431799129079809] Placed entity 18 at position (6, 4)
[CheckerBoard_431799129079809] Created entity ID:18 ConfigID:101 at (6, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 18: (1,4)->(6,4), GridID:4->34
[CheckerBoard_431799129079809] Placed entity 19 at position (6, 5)
[CheckerBoard_431799129079809] Created entity ID:19 ConfigID:101 at (6, 5) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 19: (1,5)->(6,5), GridID:5->35
[CheckerBoard_431799129079809] Placed entity 20 at position (6, 6)
[CheckerBoard_431799129079809] Created entity ID:20 ConfigID:101 at (6, 6) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 20: (1,6)->(6,6), GridID:6->36
[CheckerBoard_431799129079809] Placed entity 21 at position (7, 1)
[CheckerBoard_431799129079809] Created entity ID:21 ConfigID:102 at (7, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 21: (2,1)->(7,1), GridID:7->37
[CheckerBoard_431799129079809] Placed entity 22 at position (7, 2)
[CheckerBoard_431799129079809] Created entity ID:22 ConfigID:101 at (7, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 22: (2,2)->(7,2), GridID:8->38
[CheckerBoard_431799129079809] Placed entity 23 at position (7, 3)
[CheckerBoard_431799129079809] Created entity ID:23 ConfigID:102 at (7, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 23: (2,3)->(7,3), GridID:9->39
[CheckerBoard_431799129079809] Placed entity 24 at position (7, 4)
[CheckerBoard_431799129079809] Created entity ID:24 ConfigID:102 at (7, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 24: (2,4)->(7,4), GridID:10->40
[CheckerBoard_431799129079809] Placed entity 25 at position (7, 5)
[CheckerBoard_431799129079809] Created entity ID:25 ConfigID:101 at (7, 5) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 25: (2,5)->(7,5), GridID:11->41
[CheckerBoard_431799129079809] Placed entity 26 at position (7, 6)
[CheckerBoard_431799129079809] Created entity ID:26 ConfigID:103 at (7, 6) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 26: (2,6)->(7,6), GridID:12->42
[CheckerBoard_431799129079809] Placed entity 27 at position (8, 1)
[CheckerBoard_431799129079809] Created entity ID:27 ConfigID:103 at (8, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 27: (3,1)->(8,1), GridID:13->43
[CheckerBoard_431799129079809] Placed entity 28 at position (8, 2)
[CheckerBoard_431799129079809] Created entity ID:28 ConfigID:102 at (8, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 28: (3,2)->(8,2), GridID:14->44
[CheckerBoard_431799129079809] Placed entity 29 at position (8, 3)
[CheckerBoard_431799129079809] Created entity ID:29 ConfigID:102 at (8, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 29: (3,3)->(8,3), GridID:15->45
[CheckerBoard_431799129079809] Placed entity 30 at position (8, 4)
[CheckerBoard_431799129079809] Created entity ID:30 ConfigID:103 at (8, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 30: (4,1)->(8,4), GridID:19->46
[CheckerBoard_431799129079809] Placed entity 31 at position (8, 5)
[CheckerBoard_431799129079809] Created entity ID:31 ConfigID:102 at (8, 5) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 31: (4,4)->(8,5), GridID:22->47
[CheckerBoard_431799129079809] Placed entity 32 at position (8, 6)
[CheckerBoard_431799129079809] Created entity ID:32 ConfigID:101 at (8, 6) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 32: (4,5)->(8,6), GridID:23->48
[CheckerBoard_431799129079809] Placed entity 33 at position (9, 1)
[CheckerBoard_431799129079809] Created entity ID:33 ConfigID:102 at (9, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 33: (5,3)->(9,1), GridID:27->49
[CheckerBoard_431799129079809] Placed entity 34 at position (9, 2)
[CheckerBoard_431799129079809] Created entity ID:34 ConfigID:102 at (9, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 34: (5,5)->(9,2), GridID:29->50
[AutoChessScene_431799129079809] Restored board: player:90000080171 entities:20/20
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 10106021303
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 90000080171
[PlayerManager_431799129079809] Reset all players ready status
[AutoChessScene_431799129079809] Round started with 1 battle instances
[AutoChessScene_431799129079809] Generating 5 heroes for all players in round 5
[CheckerBoard_431799129079809] Placed entity 35 at position (4, 3)
[CheckerBoard_431799129079809] Created entity ID:35 ConfigID:103 at (4, 3) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 36 at position (4, 6)
[CheckerBoard_431799129079809] Created entity ID:36 ConfigID:102 at (4, 6) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 37 at position (4, 2)
[CheckerBoard_431799129079809] Created entity ID:37 ConfigID:101 at (4, 2) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 38 at position (5, 4)
[CheckerBoard_431799129079809] Created entity ID:38 ConfigID:103 at (5, 4) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 39 at position (4, 5)
[CheckerBoard_431799129079809] Created entity ID:39 ConfigID:101 at (4, 5) for player 10106021303
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 10106021303: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 10106021303 in My area
[AutoChessScene_431799129079809] Generated 5 heroes for player 10106021303: 5 placed on board, 0 in temporary slots
[CheckerBoard_431799129079809] Placed entity 40 at position (10, 5)
[CheckerBoard_431799129079809] Created entity ID:40 ConfigID:102 at (10, 5) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 41 at position (9, 4)
[CheckerBoard_431799129079809] Created entity ID:41 ConfigID:103 at (9, 4) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 42 at position (9, 3)
[CheckerBoard_431799129079809] Created entity ID:42 ConfigID:102 at (9, 3) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 43 at position (10, 4)
[CheckerBoard_431799129079809] Created entity ID:43 ConfigID:102 at (10, 4) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 44 at position (9, 6)
[CheckerBoard_431799129079809] Created entity ID:44 ConfigID:101 at (9, 6) for player 90000080171
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 90000080171: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 90000080171 in Enemy area
[AutoChessScene_431799129079809] Generated 5 heroes for player 90000080171: 5 placed on board, 0 in temporary slots
[AutoChessScene_431799129079809] Player status: Total=4, Active=2
[AutoChessScene_431799129079809] Player 10106021303: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431799129079809] Player 90000080171: Eliminated=False, Health=2, HasInstance=True
[AutoChessScene_431799129079809] Player 90000075484: Eliminated=True, Health=0, HasInstance=False
[AutoChessScene_431799129079809] Player 90000042297: Eliminated=True, Health=0, HasInstance=False
[AutoChessScene_431799129079809] Sending RoundStart notifications to 2 active players...
[AutoChessScene_431799129079809] RoundStart board data: player:10106021303 heroes:19
[AutoChessScene_431799129079809] RoundStart board data: player:90000080171 heroes:25
[AutoChessScene_431799129079809] Sending RoundStart to Player 10106021303 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431799129079809] RoundStart board data: player:10106021303 heroes:19
[AutoChessScene_431799129079809] RoundStart board data: player:90000080171 heroes:25
[AutoChessScene_431799129079809] Sending RoundStart to Player 90000080171 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431799129079809] Successfully sent RoundStart notifications to all 2 players via NATS
[BattleService] Updated battle 431799129079809 state to StateRoundStart
[AutoChessScene_431799129079809] State change sent to GameServer: StateRoundSettlement -> StateRoundStart (R5)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleStateManager_431799129079809] ===== ROUND 5 INITIALIZATION COMPLETE =====
[BattleStateManager_431799129079809] State: StateRoundStart -> StatePreparation (R5, 65000ms)
[AutoChessScene_431799129079809] Preparation phase started
[PlayerManager_431799129079809] Player 90000080171 ready status set to True
[AutoChessScene_431799129079809] Auto-ready 1 additional bots
[AutoChessScene_431799129079809] Free operation phase started
[BattleService] Updated battle 431799129079809 state to StatePreparation
[AutoChessScene_431799129079809] State change sent to GameServer: StateRoundStart -> StatePreparation (R5)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_431799129079809] MergeHero operation: Player 10106021303, From GridID 8 → To GridID 2
[BattleInstance] 431799129079809_1 Processing 7 move operations before merge for player 10106021303
[CheckerBoard_431799129079809] Cannot move: target position (2, 3) is occupied by entity 9
[BattleInstance] 431799129079809_1 Move operation: 10 -> 9, success: False
[CheckerBoard_431799129079809] Cannot move: target position (2, 4) is occupied by entity 10
[BattleInstance] 431799129079809_1 Move operation: 3 -> 10, success: False
[CheckerBoard_431799129079809] Cannot move: target position (1, 5) is occupied by entity 5
[BattleInstance] 431799129079809_1 Move operation: 10 -> 5, success: False
[CheckerBoard_431799129079809] Cannot move: target position (2, 3) is occupied by entity 9
[BattleInstance] 431799129079809_1 Move operation: 5 -> 9, success: False
[CheckerBoard_431799129079809] Cannot move: target position (2, 5) is occupied by entity 11
[BattleInstance] 431799129079809_1 Move operation: 4 -> 11, success: False
[CheckerBoard_431799129079809] Cannot move: target position (2, 5) is occupied by entity 11
[BattleInstance] 431799129079809_1 Move operation: 12 -> 11, success: False
[CheckerBoard_431799129079809] Cannot move: target position (2, 6) is occupied by entity 12
[BattleInstance] 431799129079809_1 Move operation: 6 -> 12, success: False
[CheckerBoard_431799129079809] Merged entity from (2, 2) to (1, 2). Star level increased from 1 to 2
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_431799129079809] Processing 1 move operations before setting player 10106021303 ready
[CheckerBoard_431799129079809] Cannot move: target position (4, 3) is occupied by entity 35
[AutoChessScene_431799129079809] Player 10106021303 move operation: GridID(2->21) → Coord((1,2)->(4,3)), success: False
[AutoChessScene_431799129079809] Failed to execute move operation for player 10106021303: GridID(2->21) → Coord((1,2)->(4,3))
[PlayerManager_431799129079809] Player 10106021303 ready status set to True
[PlayerManager_431799129079809] All players are ready!
[AutoChessScene_431799129079809] All players are ready, transitioning to next state
[BattleStateManager_431799129079809] State: StatePreparation -> StateBattleStarting (R5, 1000ms)
[AutoChessScene_431799129079809] Applying battle start buffs for all players
[BuffManager_431799129079809] Applying battle start buff 103 (Buff_103) for player 10106021303
[BuffManager_431799129079809] Applying battle start buff 109 (Buff_109) for player 10106021303
[BuffManager_431799129079809] Applying battle start buff 104 (Buff_104) for player 90000080171
[BuffManager_431799129079809] Applying battle start buff 110 (Buff_110) for player 90000080171
[AutoChessScene_431799129079809] Camp info for player 10106021303: 18 heroes added
[AutoChessScene_431799129079809] Camp info for player 90000080171: 25 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 10106021303, Team order: [10106021303, 90000080171], total GridIDs used: 43
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 10106021303 vs Opponent 90000080171 with 2 teams
[AutoChessScene_431799129079809] Camp info for player 10106021303: 18 heroes added
[AutoChessScene_431799129079809] Camp info for player 90000080171: 25 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 90000080171, Team order: [10106021303, 90000080171], total GridIDs used: 43
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 90000080171 vs Opponent 10106021303 with 2 teams
[AutoChessScene_431799129079809] Sent RoundBattleStart notifications with seed: 2117018459
[BattleService] Updated battle 431799129079809 state to StateBattleStarting
[AutoChessScene_431799129079809] State change sent to GameServer: StatePreparation -> StateBattleStarting (R5)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[AutoChessScene_431799129079809] Player 10106021303 set ready status to True
[BattleStateManager_431799129079809] State: StateBattleStarting -> StateBattleInProgress (R5, 65000ms)
[AutoChessScene_431799129079809] Starting all battle instances
[BattleInstance] 431799129079809_1 battle started
[BattleInstanceManager] Started all 1 battle instances
[AutoChessScene_431799129079809] Bot 90000080171 vs real player 10106021303, waiting for real player result
[AutoChessScene_431799129079809] Bot vs real player battles will be handled by system timeout (65s)
[BattleService] Updated battle 431799129079809 state to StateBattleInProgress
[AutoChessScene_431799129079809] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R5)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_431799129079809] Battle timeout occurred in state StateBattleInProgress
[AutoChessScene_431799129079809] Battle timeout - force ending all unfinished battles
[AutoChessScene_431799129079809] Force timeout: Real player vs bot battle, random result: winner 90000080171
[AutoChessScene_431799129079809] Force ending battle instance 431799129079809_1 due to timeout
[AutoChessScene_431799129079809] Player 90000080171 sent EndBattleReq (win: True), instance: 431799129079809_1
[AutoChessScene_431799129079809] Waiting for opponent 10106021303 to send EndBattleReq for instance 431799129079809_1
[AutoChessScene_431799129079809] Player 10106021303 sent EndBattleReq (win: False), instance: 431799129079809_1
[BattleInstance] 431799129079809_1 battle finished, winner: 90000080171, loser: 10106021303
[AutoChessScene_431799129079809] Battle instance 431799129079809_1 completed: Winner 90000080171, Loser 10106021303
[AutoChessScene_431799129079809] All battle instances finished, proceeding to settlement
[BattleStateManager_431799129079809] State: StateBattleInProgress -> StateRoundSettlement (R5, 5000ms)
[AutoChessScene_431799129079809] Processing battle results
[PlayerManager_431799129079809] Player 10106021303 health reduced by 1, current health: 2
[AutoChessScene_431799129079809] Player 10106021303 lost 1 health, winner: 90000080171
[AutoChessScene_431799129079809] Sent RoundBattleEnd notification (isEnd=false) - Winner: 90000080171, Loser: 10106021303
[AutoChessScene_431799129079809] Checking players elimination
[AutoChessScene_431799129079809] Active players remaining: 2
[AutoChessScene_431799129079809] Waiting for all players to confirm round settlement before starting new round
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000080171
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000075484
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000042297
[BattleService] Updated battle 431799129079809 state to StateRoundSettlement
[AutoChessScene_431799129079809] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R5)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleStateManager_431799129079809] Invalid state transition from StateRoundSettlement to StateRoundSettlement
[BattleStateManager_431799129079809] Published BattleTimeoutEvent for state StateRoundSettlement
[BattleStateManager_431799129079809] Round settlement timeout, waiting for AutoChessScene to handle next state
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10106021303 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10106021303 confirmed round settlement, current state: StateRoundSettlement
[AutoChessScene_431799129079809] Player 10106021303 confirmed round settlement, count: 4
[AutoChessScene_431799129079809] Real player 10106021303 confirmed, auto-confirming all bots
[AutoChessScene_431799129079809] All players confirmed round settlement, starting new round
[PlayerManager_431799129079809] Player 10106021303 ready status set to False
[PlayerManager_431799129079809] Player 90000080171 ready status set to False
[BattleStateManager_431799129079809] ===== STARTING NEW ROUND 6 =====
[BattleStateManager_431799129079809] Round 6 has buff selection: True
[BattleStateManager_431799129079809] Publishing RoundStartedEvent for round 6
[AutoChessScene_431799129079809] Round 6 started
[BattleStateManager_431799129079809] Setting state to StateRoundStart for round 6
[BattleStateManager_431799129079809] State: StateRoundSettlement -> StateRoundStart (R6, 1000ms)
[AutoChessScene_431799129079809] HandleRoundStart: 2 active players
[AutoChessScene_431799129079809] Valid player: 10106021303, Health: 2
[AutoChessScene_431799129079809] Valid player: 90000080171, Health: 2
[AutoChessScene_431799129079809] Player 10106021303 has 18 entities to save
[PlayerManager_431799129079809] Saved board data: player:10106021303 entities:18
[PlayerManager_431799129079809] Saved prev round data: player:10106021303 entities:18
[AutoChessScene_431799129079809] Player 90000080171 has 25 entities to save
[PlayerManager_431799129079809] Saved board data: player:90000080171 entities:25
[PlayerManager_431799129079809] Saved prev round data: player:90000080171 entities:25
[OpponentPairManager] Generating opponent pairs for round 1, active players: 2, eliminated: 0
[OpponentPairManager] Random pair: Player 90000080171 vs Player 10106021303
[OpponentPairManager] Generated 1 opponent pairs for round 1
[AutoChessScene_431799129079809] Created 2 opponent pairs
[PlayerManager_431799129079809] Set player opponents, count: 2
[BattleInstanceManager] Created instance 431799129079809_1 for active players 90000080171 vs 10106021303
[CheckerBoard_431799129079809] Cleared checkerboard
[CheckerBoard_431799129079809] Initialized
[BattleInstance] 431799129079809_1 created with players: 90000080171, 10106021303
[BattleInstanceManager] Created 1 battle instances for 2 players
[AutoChessScene_431799129079809] Restoring player 10106021303 to Enemy area (rows 6-10) based on current instance position
[CheckerBoard_431799129079809] Placed entity 1 at position (6, 1)
[CheckerBoard_431799129079809] Created entity ID:1 ConfigID:101 at (6, 1) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 1: (1,1)->(6,1), GridID:1->31
[CheckerBoard_431799129079809] Placed entity 2 at position (6, 2)
[CheckerBoard_431799129079809] Created entity ID:2 ConfigID:103 at (6, 2) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 2: (1,2)->(6,2), GridID:2->32
[CheckerBoard_431799129079809] Placed entity 3 at position (6, 3)
[CheckerBoard_431799129079809] Created entity ID:3 ConfigID:102 at (6, 3) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 3: (1,3)->(6,3), GridID:3->33
[CheckerBoard_431799129079809] Placed entity 4 at position (6, 4)
[CheckerBoard_431799129079809] Created entity ID:4 ConfigID:103 at (6, 4) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 4: (1,4)->(6,4), GridID:4->34
[CheckerBoard_431799129079809] Placed entity 5 at position (6, 5)
[CheckerBoard_431799129079809] Created entity ID:5 ConfigID:102 at (6, 5) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 5: (1,5)->(6,5), GridID:5->35
[CheckerBoard_431799129079809] Placed entity 6 at position (6, 6)
[CheckerBoard_431799129079809] Created entity ID:6 ConfigID:103 at (6, 6) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 6: (1,6)->(6,6), GridID:6->36
[CheckerBoard_431799129079809] Placed entity 7 at position (7, 1)
[CheckerBoard_431799129079809] Created entity ID:7 ConfigID:101 at (7, 1) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 7: (2,1)->(7,1), GridID:7->37
[CheckerBoard_431799129079809] Placed entity 8 at position (7, 2)
[CheckerBoard_431799129079809] Created entity ID:8 ConfigID:102 at (7, 2) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 8: (2,3)->(7,2), GridID:9->38
[CheckerBoard_431799129079809] Placed entity 9 at position (7, 3)
[CheckerBoard_431799129079809] Created entity ID:9 ConfigID:102 at (7, 3) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 9: (2,4)->(7,3), GridID:10->39
[CheckerBoard_431799129079809] Placed entity 10 at position (7, 4)
[CheckerBoard_431799129079809] Created entity ID:10 ConfigID:103 at (7, 4) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 10: (2,5)->(7,4), GridID:11->40
[CheckerBoard_431799129079809] Placed entity 11 at position (7, 5)
[CheckerBoard_431799129079809] Created entity ID:11 ConfigID:103 at (7, 5) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 11: (2,6)->(7,5), GridID:12->41
[CheckerBoard_431799129079809] Placed entity 12 at position (7, 6)
[CheckerBoard_431799129079809] Created entity ID:12 ConfigID:102 at (7, 6) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 12: (3,1)->(7,6), GridID:13->42
[CheckerBoard_431799129079809] Placed entity 13 at position (8, 1)
[CheckerBoard_431799129079809] Created entity ID:13 ConfigID:101 at (8, 1) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 13: (3,2)->(8,1), GridID:14->43
[CheckerBoard_431799129079809] Placed entity 14 at position (8, 2)
[CheckerBoard_431799129079809] Created entity ID:14 ConfigID:101 at (8, 2) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 14: (4,2)->(8,2), GridID:20->44
[CheckerBoard_431799129079809] Placed entity 15 at position (8, 3)
[CheckerBoard_431799129079809] Created entity ID:15 ConfigID:103 at (8, 3) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 15: (4,3)->(8,3), GridID:21->45
[CheckerBoard_431799129079809] Placed entity 16 at position (8, 4)
[CheckerBoard_431799129079809] Created entity ID:16 ConfigID:101 at (8, 4) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 16: (4,5)->(8,4), GridID:23->46
[CheckerBoard_431799129079809] Placed entity 17 at position (8, 5)
[CheckerBoard_431799129079809] Created entity ID:17 ConfigID:102 at (8, 5) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 17: (4,6)->(8,5), GridID:24->47
[CheckerBoard_431799129079809] Placed entity 18 at position (8, 6)
[CheckerBoard_431799129079809] Created entity ID:18 ConfigID:103 at (8, 6) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 18: (5,4)->(8,6), GridID:28->48
[AutoChessScene_431799129079809] Restored board: player:10106021303 entities:18/18
[AutoChessScene_431799129079809] Restoring player 90000080171 to My area (rows 1-5) based on current instance position
[CheckerBoard_431799129079809] Placed entity 19 at position (1, 1)
[CheckerBoard_431799129079809] Created entity ID:19 ConfigID:102 at (1, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 19: (6,1)->(1,1), GridID:31->1
[CheckerBoard_431799129079809] Placed entity 20 at position (1, 2)
[CheckerBoard_431799129079809] Created entity ID:20 ConfigID:101 at (1, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 20: (6,2)->(1,2), GridID:32->2
[CheckerBoard_431799129079809] Placed entity 21 at position (1, 3)
[CheckerBoard_431799129079809] Created entity ID:21 ConfigID:101 at (1, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 21: (6,3)->(1,3), GridID:33->3
[CheckerBoard_431799129079809] Placed entity 22 at position (1, 4)
[CheckerBoard_431799129079809] Created entity ID:22 ConfigID:101 at (1, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 22: (6,4)->(1,4), GridID:34->4
[CheckerBoard_431799129079809] Placed entity 23 at position (1, 5)
[CheckerBoard_431799129079809] Created entity ID:23 ConfigID:101 at (1, 5) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 23: (6,5)->(1,5), GridID:35->5
[CheckerBoard_431799129079809] Placed entity 24 at position (1, 6)
[CheckerBoard_431799129079809] Created entity ID:24 ConfigID:101 at (1, 6) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 24: (6,6)->(1,6), GridID:36->6
[CheckerBoard_431799129079809] Placed entity 25 at position (2, 1)
[CheckerBoard_431799129079809] Created entity ID:25 ConfigID:102 at (2, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 25: (7,1)->(2,1), GridID:37->7
[CheckerBoard_431799129079809] Placed entity 26 at position (2, 2)
[CheckerBoard_431799129079809] Created entity ID:26 ConfigID:101 at (2, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 26: (7,2)->(2,2), GridID:38->8
[CheckerBoard_431799129079809] Placed entity 27 at position (2, 3)
[CheckerBoard_431799129079809] Created entity ID:27 ConfigID:102 at (2, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 27: (7,3)->(2,3), GridID:39->9
[CheckerBoard_431799129079809] Placed entity 28 at position (2, 4)
[CheckerBoard_431799129079809] Created entity ID:28 ConfigID:102 at (2, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 28: (7,4)->(2,4), GridID:40->10
[CheckerBoard_431799129079809] Placed entity 29 at position (2, 5)
[CheckerBoard_431799129079809] Created entity ID:29 ConfigID:101 at (2, 5) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 29: (7,5)->(2,5), GridID:41->11
[CheckerBoard_431799129079809] Placed entity 30 at position (2, 6)
[CheckerBoard_431799129079809] Created entity ID:30 ConfigID:103 at (2, 6) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 30: (7,6)->(2,6), GridID:42->12
[CheckerBoard_431799129079809] Placed entity 31 at position (3, 1)
[CheckerBoard_431799129079809] Created entity ID:31 ConfigID:103 at (3, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 31: (8,1)->(3,1), GridID:43->13
[CheckerBoard_431799129079809] Placed entity 32 at position (3, 2)
[CheckerBoard_431799129079809] Created entity ID:32 ConfigID:102 at (3, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 32: (8,2)->(3,2), GridID:44->14
[CheckerBoard_431799129079809] Placed entity 33 at position (3, 3)
[CheckerBoard_431799129079809] Created entity ID:33 ConfigID:102 at (3, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 33: (8,3)->(3,3), GridID:45->15
[CheckerBoard_431799129079809] Placed entity 34 at position (3, 4)
[CheckerBoard_431799129079809] Created entity ID:34 ConfigID:103 at (3, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 34: (8,4)->(3,4), GridID:46->16
[CheckerBoard_431799129079809] Placed entity 35 at position (3, 5)
[CheckerBoard_431799129079809] Created entity ID:35 ConfigID:102 at (3, 5) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 35: (8,5)->(3,5), GridID:47->17
[CheckerBoard_431799129079809] Placed entity 36 at position (3, 6)
[CheckerBoard_431799129079809] Created entity ID:36 ConfigID:101 at (3, 6) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 36: (8,6)->(3,6), GridID:48->18
[CheckerBoard_431799129079809] Placed entity 37 at position (4, 1)
[CheckerBoard_431799129079809] Created entity ID:37 ConfigID:102 at (4, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 37: (9,1)->(4,1), GridID:49->19
[CheckerBoard_431799129079809] Placed entity 38 at position (4, 2)
[CheckerBoard_431799129079809] Created entity ID:38 ConfigID:102 at (4, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 38: (9,2)->(4,2), GridID:50->20
[CheckerBoard_431799129079809] Placed entity 39 at position (4, 3)
[CheckerBoard_431799129079809] Created entity ID:39 ConfigID:102 at (4, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 39: (9,3)->(4,3), GridID:51->21
[CheckerBoard_431799129079809] Placed entity 40 at position (4, 4)
[CheckerBoard_431799129079809] Created entity ID:40 ConfigID:103 at (4, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 40: (9,4)->(4,4), GridID:52->22
[CheckerBoard_431799129079809] Placed entity 41 at position (4, 5)
[CheckerBoard_431799129079809] Created entity ID:41 ConfigID:101 at (4, 5) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 41: (9,6)->(4,5), GridID:54->23
[CheckerBoard_431799129079809] Placed entity 42 at position (4, 6)
[CheckerBoard_431799129079809] Created entity ID:42 ConfigID:102 at (4, 6) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 42: (10,4)->(4,6), GridID:58->24
[CheckerBoard_431799129079809] Placed entity 43 at position (5, 1)
[CheckerBoard_431799129079809] Created entity ID:43 ConfigID:102 at (5, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 43: (10,5)->(5,1), GridID:59->25
[AutoChessScene_431799129079809] Restored board: player:90000080171 entities:25/25
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 10106021303
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 90000080171
[PlayerManager_431799129079809] Reset all players ready status
[AutoChessScene_431799129079809] Round started with 1 battle instances
[AutoChessScene_431799129079809] Generating buff options for all players
[BuffManager_431799129079809] Generated 3 buff options for player 10106021303: [108, 106, 110]
[AutoChessScene_431799129079809] Generated 3 buff options for player 10106021303: [108, 106, 110]
[BuffManager_431799129079809] Generated 3 buff options for player 90000080171: [102, 108, 101]
[AutoChessScene_431799129079809] Generated 3 buff options for player 90000080171: [102, 108, 101]
[AutoChessScene_431799129079809] Player status: Total=4, Active=2
[AutoChessScene_431799129079809] Player 10106021303: Eliminated=False, Health=2, HasInstance=True
[AutoChessScene_431799129079809] Player 90000080171: Eliminated=False, Health=2, HasInstance=True
[AutoChessScene_431799129079809] Player 90000075484: Eliminated=True, Health=0, HasInstance=False
[AutoChessScene_431799129079809] Player 90000042297: Eliminated=True, Health=0, HasInstance=False
[AutoChessScene_431799129079809] Sending RoundStart notifications to 2 active players...
[AutoChessScene_431799129079809] RoundStart: Player 10106021303 buff options: [108, 106, 110]
[AutoChessScene_431799129079809] RoundStart board data: player:90000080171 heroes:25
[AutoChessScene_431799129079809] RoundStart board data: player:10106021303 heroes:18
[AutoChessScene_431799129079809] Sending RoundStart to Player 10106021303 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_431799129079809] RoundStart: Player 90000080171 buff options: [102, 108, 101]
[AutoChessScene_431799129079809] RoundStart board data: player:90000080171 heroes:25
[AutoChessScene_431799129079809] RoundStart board data: player:10106021303 heroes:18
[AutoChessScene_431799129079809] Sending RoundStart to Player 90000080171 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_431799129079809] Successfully sent RoundStart notifications to all 2 players via NATS
[BattleService] Updated battle 431799129079809 state to StateRoundStart
[AutoChessScene_431799129079809] State change sent to GameServer: StateRoundSettlement -> StateRoundStart (R6)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleStateManager_431799129079809] ===== ROUND 6 INITIALIZATION COMPLETE =====
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleStateManager_431799129079809] Buff selection timer started: 25000ms
[BattleStateManager_431799129079809] State: StateRoundStart -> StatePreparation (R6, 65000ms)
[AutoChessScene_431799129079809] Preparation phase started
[BuffManager_431799129079809] Added buff 102 (Buff_102) to player 90000080171
[AutoChessScene_431799129079809] Auto-selected buff 102 for bot player 90000080171
[CheckerBoard_431799129079809] Placed entity 44 at position (5, 3)
[CheckerBoard_431799129079809] Created entity ID:44 ConfigID:101 at (5, 3) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 45 at position (5, 4)
[CheckerBoard_431799129079809] Created entity ID:45 ConfigID:103 at (5, 4) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 46 at position (5, 6)
[CheckerBoard_431799129079809] Created entity ID:46 ConfigID:103 at (5, 6) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 47 at position (5, 5)
[CheckerBoard_431799129079809] Created entity ID:47 ConfigID:103 at (5, 5) for player 90000080171
[CheckerBoard_431799129079809] Placed entity 48 at position (5, 2)
[CheckerBoard_431799129079809] Created entity ID:48 ConfigID:103 at (5, 2) for player 90000080171
[CheckerBoard_431799129079809] CheckTimes limit (4) reached for 1 hero types for player 90000080171
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 90000080171: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 90000080171 in My area
[AutoChessScene_431799129079809] Generated 5 heroes for player 90000080171: 5 placed on board, 0 in temporary slots
[AutoChessScene_431799129079809] Generated 5 new heroes for bot player 90000080171 after buff selection
[AutoChessScene_431799129079809] No buff options available for bot player 90000075484
[AutoChessScene_431799129079809] No buff options available for bot player 90000042297
[PlayerManager_431799129079809] Player 90000080171 ready status set to True
[AutoChessScene_431799129079809] Auto-ready 1 additional bots
[AutoChessScene_431799129079809] Free operation phase started
[BattleService] Updated battle 431799129079809 state to StatePreparation
[AutoChessScene_431799129079809] State change sent to GameServer: StateRoundStart -> StatePreparation (R6)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED SelectBuffer RPC =====
[BattleService] Player 10106021303 is selecting buff 106
[BuffManager_431799129079809] Added buff 106 (Buff_106) to player 10106021303
[CheckerBoard_431799129079809] Placed entity 49 at position (10, 6)
[CheckerBoard_431799129079809] Created entity ID:49 ConfigID:102 at (10, 6) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 50 at position (9, 5)
[CheckerBoard_431799129079809] Created entity ID:50 ConfigID:103 at (9, 5) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 51 at position (9, 6)
[CheckerBoard_431799129079809] Created entity ID:51 ConfigID:101 at (9, 6) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 52 at position (9, 2)
[CheckerBoard_431799129079809] Created entity ID:52 ConfigID:102 at (9, 2) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 53 at position (10, 4)
[CheckerBoard_431799129079809] Created entity ID:53 ConfigID:101 at (10, 4) for player 10106021303
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 10106021303: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 10106021303 in Enemy area
[AutoChessScene_431799129079809] Generated 5 heroes for player 10106021303: 5 placed on board, 0 in temporary slots
[AutoChessScene_431799129079809] Player 10106021303 selected buff 106, generated 5 new heroes
[BattleService] SelectBuffer response: Code=0, NewHeroes=5
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_431799129079809] Buff selection timeout occurred - forcing buff selection for all unselected players
[BattleStateManager_431799129079809] Buff selection timeout triggered
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_431799129079809] Battle timeout occurred in state StatePreparation
[AutoChessScene_431799129079809] Preparation timeout - force ready all unready players
[PlayerManager_431799129079809] Player 10106021303 ready status set to True
[PlayerManager_431799129079809] All players are ready!
[AutoChessScene_431799129079809] All players are ready, transitioning to next state
[BattleStateManager_431799129079809] State: StatePreparation -> StateBattleStarting (R6, 1000ms)
[AutoChessScene_431799129079809] Applying battle start buffs for all players
[BuffManager_431799129079809] Applying battle start buff 103 (Buff_103) for player 10106021303
[BuffManager_431799129079809] Applying battle start buff 109 (Buff_109) for player 10106021303
[BuffManager_431799129079809] Applying battle start buff 106 (Buff_106) for player 10106021303
[BuffManager_431799129079809] Applying battle start buff 104 (Buff_104) for player 90000080171
[BuffManager_431799129079809] Applying battle start buff 110 (Buff_110) for player 90000080171
[BuffManager_431799129079809] Applying battle start buff 102 (Buff_102) for player 90000080171
[AutoChessScene_431799129079809] Camp info for player 90000080171: 30 heroes added
[AutoChessScene_431799129079809] Camp info for player 10106021303: 23 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 10106021303, Team order: [90000080171, 10106021303], total GridIDs used: 53
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 10106021303 vs Opponent 90000080171 with 2 teams
[AutoChessScene_431799129079809] Camp info for player 90000080171: 30 heroes added
[AutoChessScene_431799129079809] Camp info for player 10106021303: 23 heroes added
[AutoChessScene_431799129079809] Created RoundBattleStart request for player 90000080171, Team order: [90000080171, 10106021303], total GridIDs used: 53
[AutoChessScene_431799129079809] Sent RoundBattleStart to Player 90000080171 vs Opponent 10106021303 with 2 teams
[AutoChessScene_431799129079809] Sent RoundBattleStart notifications with seed: 1397881288
[BattleService] Updated battle 431799129079809 state to StateBattleStarting
[AutoChessScene_431799129079809] State change sent to GameServer: StatePreparation -> StateBattleStarting (R6)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[AutoChessScene_431799129079809] Force ready for player 10106021303 due to preparation timeout
[BattleStateManager_431799129079809] Published BattleTimeoutEvent for state StateBattleStarting
[BattleStateManager_431799129079809] State: StateBattleStarting -> StateBattleInProgress (R6, 65000ms)
[AutoChessScene_431799129079809] Starting all battle instances
[BattleInstance] 431799129079809_1 battle started
[BattleInstanceManager] Started all 1 battle instances
[AutoChessScene_431799129079809] Bot 90000080171 vs real player 10106021303, waiting for real player result
[AutoChessScene_431799129079809] Bot vs real player battles will be handled by system timeout (65s)
[BattleService] Updated battle 431799129079809 state to StateBattleInProgress
[AutoChessScene_431799129079809] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R6)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_431799129079809] Cannot merge hero in state StateBattleInProgress
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_431799129079809] Battle timeout occurred in state StateBattleInProgress
[AutoChessScene_431799129079809] Battle timeout - force ending all unfinished battles
[AutoChessScene_431799129079809] Force timeout: Real player vs bot battle, random result: winner 10106021303
[AutoChessScene_431799129079809] Force ending battle instance 431799129079809_1 due to timeout
[AutoChessScene_431799129079809] Player 10106021303 sent EndBattleReq (win: True), instance: 431799129079809_1
[AutoChessScene_431799129079809] Auto EndBattle for bot 90000080171 vs real player 10106021303, bot result: False
[BattleInstance] 431799129079809_1 battle finished, winner: 10106021303, loser: 90000080171
[AutoChessScene_431799129079809] Battle instance 431799129079809_1 completed: Winner 10106021303, Loser 90000080171
[AutoChessScene_431799129079809] All battle instances finished, proceeding to settlement
[BattleStateManager_431799129079809] State: StateBattleInProgress -> StateRoundSettlement (R6, 5000ms)
[AutoChessScene_431799129079809] Processing battle results
[PlayerManager_431799129079809] Player 90000080171 health reduced by 1, current health: 1
[AutoChessScene_431799129079809] Player 90000080171 lost 1 health, winner: 10106021303
[AutoChessScene_431799129079809] Sent RoundBattleEnd notification (isEnd=false) - Winner: 10106021303, Loser: 90000080171
[AutoChessScene_431799129079809] Checking players elimination
[AutoChessScene_431799129079809] Active players remaining: 2
[AutoChessScene_431799129079809] Waiting for all players to confirm round settlement before starting new round
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000080171
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000075484
[AutoChessScene_431799129079809] Auto-confirming round settlement for bot 90000042297
[BattleService] Updated battle 431799129079809 state to StateRoundSettlement
[AutoChessScene_431799129079809] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R6)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[AutoChessScene_431799129079809] Player 90000080171 sent EndBattleReq (win: False), instance: 431799129079809_1
[AutoChessScene_431799129079809] Waiting for opponent 10106021303 to send EndBattleReq for instance 431799129079809_1
[BattleStateManager_431799129079809] Invalid state transition from StateRoundSettlement to StateRoundSettlement
[BattleStateManager_431799129079809] Published BattleTimeoutEvent for state StateRoundSettlement
[BattleStateManager_431799129079809] Round settlement timeout, waiting for AutoChessScene to handle next state
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10106021303 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10106021303 confirmed round settlement, current state: StateRoundSettlement
[AutoChessScene_431799129079809] Player 10106021303 confirmed round settlement, count: 4
[AutoChessScene_431799129079809] Real player 10106021303 confirmed, auto-confirming all bots
[AutoChessScene_431799129079809] All players confirmed round settlement, starting new round
[PlayerManager_431799129079809] Player 10106021303 ready status set to False
[PlayerManager_431799129079809] Player 90000080171 ready status set to False
[BattleStateManager_431799129079809] ===== STARTING NEW ROUND 7 =====
[BattleStateManager_431799129079809] Round 7 has buff selection: False
[BattleStateManager_431799129079809] Publishing RoundStartedEvent for round 7
[AutoChessScene_431799129079809] Round 7 started
[BattleStateManager_431799129079809] Setting state to StateRoundStart for round 7
[BattleStateManager_431799129079809] State: StateRoundSettlement -> StateRoundStart (R7, 1000ms)
[AutoChessScene_431799129079809] HandleRoundStart: 2 active players
[AutoChessScene_431799129079809] Valid player: 10106021303, Health: 2
[AutoChessScene_431799129079809] Valid player: 90000080171, Health: 1
[AutoChessScene_431799129079809] Player 10106021303 has 23 entities to save
[PlayerManager_431799129079809] Saved board data: player:10106021303 entities:23
[PlayerManager_431799129079809] Saved prev round data: player:10106021303 entities:23
[AutoChessScene_431799129079809] Player 90000080171 has 30 entities to save
[PlayerManager_431799129079809] Saved board data: player:90000080171 entities:30
[PlayerManager_431799129079809] Saved prev round data: player:90000080171 entities:30
[OpponentPairManager] Generating opponent pairs for round 1, active players: 2, eliminated: 0
[OpponentPairManager] Random pair: Player 10106021303 vs Player 90000080171
[OpponentPairManager] Generated 1 opponent pairs for round 1
[AutoChessScene_431799129079809] Created 2 opponent pairs
[PlayerManager_431799129079809] Set player opponents, count: 2
[BattleInstanceManager] Created instance 431799129079809_1 for active players 10106021303 vs 90000080171
[CheckerBoard_431799129079809] Cleared checkerboard
[CheckerBoard_431799129079809] Initialized
[BattleInstance] 431799129079809_1 created with players: 10106021303, 90000080171
[BattleInstanceManager] Created 1 battle instances for 2 players
[AutoChessScene_431799129079809] Restoring player 10106021303 to My area (rows 1-5) based on current instance position
[CheckerBoard_431799129079809] Placed entity 1 at position (1, 1)
[CheckerBoard_431799129079809] Created entity ID:1 ConfigID:101 at (1, 1) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 1: (6,1)->(1,1), GridID:31->1
[CheckerBoard_431799129079809] Placed entity 2 at position (1, 2)
[CheckerBoard_431799129079809] Created entity ID:2 ConfigID:103 at (1, 2) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 2: (6,2)->(1,2), GridID:32->2
[CheckerBoard_431799129079809] Placed entity 3 at position (1, 3)
[CheckerBoard_431799129079809] Created entity ID:3 ConfigID:102 at (1, 3) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 3: (6,3)->(1,3), GridID:33->3
[CheckerBoard_431799129079809] Placed entity 4 at position (1, 4)
[CheckerBoard_431799129079809] Created entity ID:4 ConfigID:103 at (1, 4) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 4: (6,4)->(1,4), GridID:34->4
[CheckerBoard_431799129079809] Placed entity 5 at position (1, 5)
[CheckerBoard_431799129079809] Created entity ID:5 ConfigID:102 at (1, 5) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 5: (6,5)->(1,5), GridID:35->5
[CheckerBoard_431799129079809] Placed entity 6 at position (1, 6)
[CheckerBoard_431799129079809] Created entity ID:6 ConfigID:103 at (1, 6) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 6: (6,6)->(1,6), GridID:36->6
[CheckerBoard_431799129079809] Placed entity 7 at position (2, 1)
[CheckerBoard_431799129079809] Created entity ID:7 ConfigID:101 at (2, 1) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 7: (7,1)->(2,1), GridID:37->7
[CheckerBoard_431799129079809] Placed entity 8 at position (2, 2)
[CheckerBoard_431799129079809] Created entity ID:8 ConfigID:102 at (2, 2) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 8: (7,2)->(2,2), GridID:38->8
[CheckerBoard_431799129079809] Placed entity 9 at position (2, 3)
[CheckerBoard_431799129079809] Created entity ID:9 ConfigID:102 at (2, 3) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 9: (7,3)->(2,3), GridID:39->9
[CheckerBoard_431799129079809] Placed entity 10 at position (2, 4)
[CheckerBoard_431799129079809] Created entity ID:10 ConfigID:103 at (2, 4) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 10: (7,4)->(2,4), GridID:40->10
[CheckerBoard_431799129079809] Placed entity 11 at position (2, 5)
[CheckerBoard_431799129079809] Created entity ID:11 ConfigID:103 at (2, 5) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 11: (7,5)->(2,5), GridID:41->11
[CheckerBoard_431799129079809] Placed entity 12 at position (2, 6)
[CheckerBoard_431799129079809] Created entity ID:12 ConfigID:102 at (2, 6) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 12: (7,6)->(2,6), GridID:42->12
[CheckerBoard_431799129079809] Placed entity 13 at position (3, 1)
[CheckerBoard_431799129079809] Created entity ID:13 ConfigID:101 at (3, 1) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 13: (8,1)->(3,1), GridID:43->13
[CheckerBoard_431799129079809] Placed entity 14 at position (3, 2)
[CheckerBoard_431799129079809] Created entity ID:14 ConfigID:101 at (3, 2) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 14: (8,2)->(3,2), GridID:44->14
[CheckerBoard_431799129079809] Placed entity 15 at position (3, 3)
[CheckerBoard_431799129079809] Created entity ID:15 ConfigID:103 at (3, 3) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 15: (8,3)->(3,3), GridID:45->15
[CheckerBoard_431799129079809] Placed entity 16 at position (3, 4)
[CheckerBoard_431799129079809] Created entity ID:16 ConfigID:101 at (3, 4) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 16: (8,4)->(3,4), GridID:46->16
[CheckerBoard_431799129079809] Placed entity 17 at position (3, 5)
[CheckerBoard_431799129079809] Created entity ID:17 ConfigID:102 at (3, 5) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 17: (8,5)->(3,5), GridID:47->17
[CheckerBoard_431799129079809] Placed entity 18 at position (3, 6)
[CheckerBoard_431799129079809] Created entity ID:18 ConfigID:103 at (3, 6) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 18: (8,6)->(3,6), GridID:48->18
[CheckerBoard_431799129079809] Placed entity 19 at position (4, 1)
[CheckerBoard_431799129079809] Created entity ID:19 ConfigID:102 at (4, 1) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 19: (9,2)->(4,1), GridID:50->19
[CheckerBoard_431799129079809] Placed entity 20 at position (4, 2)
[CheckerBoard_431799129079809] Created entity ID:20 ConfigID:103 at (4, 2) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 20: (9,5)->(4,2), GridID:53->20
[CheckerBoard_431799129079809] Placed entity 21 at position (4, 3)
[CheckerBoard_431799129079809] Created entity ID:21 ConfigID:101 at (4, 3) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 21: (9,6)->(4,3), GridID:54->21
[CheckerBoard_431799129079809] Placed entity 22 at position (4, 4)
[CheckerBoard_431799129079809] Created entity ID:22 ConfigID:101 at (4, 4) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 22: (10,4)->(4,4), GridID:58->22
[CheckerBoard_431799129079809] Placed entity 23 at position (4, 5)
[CheckerBoard_431799129079809] Created entity ID:23 ConfigID:102 at (4, 5) for player 10106021303
[AutoChessScene_431799129079809] Restored entity 23: (10,6)->(4,5), GridID:60->23
[AutoChessScene_431799129079809] Restored board: player:10106021303 entities:23/23
[AutoChessScene_431799129079809] Restoring player 90000080171 to Enemy area (rows 6-10) based on current instance position
[CheckerBoard_431799129079809] Placed entity 24 at position (6, 1)
[CheckerBoard_431799129079809] Created entity ID:24 ConfigID:102 at (6, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 24: (1,1)->(6,1), GridID:1->31
[CheckerBoard_431799129079809] Placed entity 25 at position (6, 2)
[CheckerBoard_431799129079809] Created entity ID:25 ConfigID:101 at (6, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 25: (1,2)->(6,2), GridID:2->32
[CheckerBoard_431799129079809] Placed entity 26 at position (6, 3)
[CheckerBoard_431799129079809] Created entity ID:26 ConfigID:101 at (6, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 26: (1,3)->(6,3), GridID:3->33
[CheckerBoard_431799129079809] Placed entity 27 at position (6, 4)
[CheckerBoard_431799129079809] Created entity ID:27 ConfigID:101 at (6, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 27: (1,4)->(6,4), GridID:4->34
[CheckerBoard_431799129079809] Placed entity 28 at position (6, 5)
[CheckerBoard_431799129079809] Created entity ID:28 ConfigID:101 at (6, 5) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 28: (1,5)->(6,5), GridID:5->35
[CheckerBoard_431799129079809] Placed entity 29 at position (6, 6)
[CheckerBoard_431799129079809] Created entity ID:29 ConfigID:101 at (6, 6) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 29: (1,6)->(6,6), GridID:6->36
[CheckerBoard_431799129079809] Placed entity 30 at position (7, 1)
[CheckerBoard_431799129079809] Created entity ID:30 ConfigID:102 at (7, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 30: (2,1)->(7,1), GridID:7->37
[CheckerBoard_431799129079809] Placed entity 31 at position (7, 2)
[CheckerBoard_431799129079809] Created entity ID:31 ConfigID:101 at (7, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 31: (2,2)->(7,2), GridID:8->38
[CheckerBoard_431799129079809] Placed entity 32 at position (7, 3)
[CheckerBoard_431799129079809] Created entity ID:32 ConfigID:102 at (7, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 32: (2,3)->(7,3), GridID:9->39
[CheckerBoard_431799129079809] Placed entity 33 at position (7, 4)
[CheckerBoard_431799129079809] Created entity ID:33 ConfigID:102 at (7, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 33: (2,4)->(7,4), GridID:10->40
[CheckerBoard_431799129079809] Placed entity 34 at position (7, 5)
[CheckerBoard_431799129079809] Created entity ID:34 ConfigID:101 at (7, 5) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 34: (2,5)->(7,5), GridID:11->41
[CheckerBoard_431799129079809] Placed entity 35 at position (7, 6)
[CheckerBoard_431799129079809] Created entity ID:35 ConfigID:103 at (7, 6) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 35: (2,6)->(7,6), GridID:12->42
[CheckerBoard_431799129079809] Placed entity 36 at position (8, 1)
[CheckerBoard_431799129079809] Created entity ID:36 ConfigID:103 at (8, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 36: (3,1)->(8,1), GridID:13->43
[CheckerBoard_431799129079809] Placed entity 37 at position (8, 2)
[CheckerBoard_431799129079809] Created entity ID:37 ConfigID:102 at (8, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 37: (3,2)->(8,2), GridID:14->44
[CheckerBoard_431799129079809] Placed entity 38 at position (8, 3)
[CheckerBoard_431799129079809] Created entity ID:38 ConfigID:102 at (8, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 38: (3,3)->(8,3), GridID:15->45
[CheckerBoard_431799129079809] Placed entity 39 at position (8, 4)
[CheckerBoard_431799129079809] Created entity ID:39 ConfigID:103 at (8, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 39: (3,4)->(8,4), GridID:16->46
[CheckerBoard_431799129079809] Placed entity 40 at position (8, 5)
[CheckerBoard_431799129079809] Created entity ID:40 ConfigID:102 at (8, 5) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 40: (3,5)->(8,5), GridID:17->47
[CheckerBoard_431799129079809] Placed entity 41 at position (8, 6)
[CheckerBoard_431799129079809] Created entity ID:41 ConfigID:101 at (8, 6) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 41: (3,6)->(8,6), GridID:18->48
[CheckerBoard_431799129079809] Placed entity 42 at position (9, 1)
[CheckerBoard_431799129079809] Created entity ID:42 ConfigID:102 at (9, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 42: (4,1)->(9,1), GridID:19->49
[CheckerBoard_431799129079809] Placed entity 43 at position (9, 2)
[CheckerBoard_431799129079809] Created entity ID:43 ConfigID:102 at (9, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 43: (4,2)->(9,2), GridID:20->50
[CheckerBoard_431799129079809] Placed entity 44 at position (9, 3)
[CheckerBoard_431799129079809] Created entity ID:44 ConfigID:102 at (9, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 44: (4,3)->(9,3), GridID:21->51
[CheckerBoard_431799129079809] Placed entity 45 at position (9, 4)
[CheckerBoard_431799129079809] Created entity ID:45 ConfigID:103 at (9, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 45: (4,4)->(9,4), GridID:22->52
[CheckerBoard_431799129079809] Placed entity 46 at position (9, 5)
[CheckerBoard_431799129079809] Created entity ID:46 ConfigID:101 at (9, 5) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 46: (4,5)->(9,5), GridID:23->53
[CheckerBoard_431799129079809] Placed entity 47 at position (9, 6)
[CheckerBoard_431799129079809] Created entity ID:47 ConfigID:102 at (9, 6) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 47: (4,6)->(9,6), GridID:24->54
[CheckerBoard_431799129079809] Placed entity 48 at position (10, 1)
[CheckerBoard_431799129079809] Created entity ID:48 ConfigID:102 at (10, 1) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 48: (5,1)->(10,1), GridID:25->55
[CheckerBoard_431799129079809] Placed entity 49 at position (10, 2)
[CheckerBoard_431799129079809] Created entity ID:49 ConfigID:103 at (10, 2) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 49: (5,2)->(10,2), GridID:26->56
[CheckerBoard_431799129079809] Placed entity 50 at position (10, 3)
[CheckerBoard_431799129079809] Created entity ID:50 ConfigID:101 at (10, 3) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 50: (5,3)->(10,3), GridID:27->57
[CheckerBoard_431799129079809] Placed entity 51 at position (10, 4)
[CheckerBoard_431799129079809] Created entity ID:51 ConfigID:103 at (10, 4) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 51: (5,4)->(10,4), GridID:28->58
[CheckerBoard_431799129079809] Placed entity 52 at position (10, 5)
[CheckerBoard_431799129079809] Created entity ID:52 ConfigID:103 at (10, 5) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 52: (5,5)->(10,5), GridID:29->59
[CheckerBoard_431799129079809] Placed entity 53 at position (10, 6)
[CheckerBoard_431799129079809] Created entity ID:53 ConfigID:103 at (10, 6) for player 90000080171
[AutoChessScene_431799129079809] Restored entity 53: (5,6)->(10,6), GridID:30->60
[AutoChessScene_431799129079809] Restored board: player:90000080171 entities:30/30
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 10106021303
[AutoChessScene_431799129079809] Cleaned orphaned entities for player 90000080171
[PlayerManager_431799129079809] Reset all players ready status
[AutoChessScene_431799129079809] Round started with 1 battle instances
[AutoChessScene_431799129079809] Generating 5 heroes for all players in round 7
[CheckerBoard_431799129079809] Placed entity 54 at position (5, 4)
[CheckerBoard_431799129079809] Created entity ID:54 ConfigID:103 at (5, 4) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 55 at position (5, 5)
[CheckerBoard_431799129079809] Created entity ID:55 ConfigID:102 at (5, 5) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 56 at position (5, 3)
[CheckerBoard_431799129079809] Created entity ID:56 ConfigID:102 at (5, 3) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 57 at position (5, 2)
[CheckerBoard_431799129079809] Created entity ID:57 ConfigID:102 at (5, 2) for player 10106021303
[CheckerBoard_431799129079809] Placed entity 58 at position (4, 6)
[CheckerBoard_431799129079809] Created entity ID:58 ConfigID:103 at (4, 6) for player 10106021303
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 10106021303: 5 placed, 0 in temporary slots
[CheckerBoard_431799129079809] Generated 5 heroes for player 10106021303 in My area
[AutoChessScene_431799129079809] Generated 5 heroes for player 10106021303: 5 placed on board, 0 in temporary slots
[CheckerBoard_431799129079809] Hero 102 placed in temporary slot for player 90000080171
[CheckerBoard_431799129079809] Hero 102 placed in temporary slot for player 90000080171
[CheckerBoard_431799129079809] Hero 102 placed in temporary slot for player 90000080171
[CheckerBoard_431799129079809] Hero 102 placed in temporary slot for player 90000080171
[CheckerBoard_431799129079809] Hero 103 placed in temporary slot for player 90000080171
[CheckerBoard_431799129079809] CheckTimes limit (4) reached for 1 hero types for player 90000080171
[CheckerBoard_431799129079809] Generated 5/5 heroes for player 90000080171: 0 placed, 5 in temporary slots
[CheckerBoard_431799129079809] Generated 0 heroes for player 90000080171 in Enemy area
[AutoChessScene_431799129079809] Generated 0 heroes for player 90000080171: 0 placed on board, 0 in temporary slots
[AutoChessScene_431799129079809] Player status: Total=4, Active=2
[AutoChessScene_431799129079809] Player 10106021303: Eliminated=False, Health=2, HasInstance=True
[AutoChessScene_431799129079809] Player 90000080171: Eliminated=False, Health=1, HasInstance=True
[AutoChessScene_431799129079809] Player 90000075484: Eliminated=True, Health=0, HasInstance=False
[AutoChessScene_431799129079809] Player 90000042297: Eliminated=True, Health=0, HasInstance=False
[AutoChessScene_431799129079809] Sending RoundStart notifications to 2 active players...
[AutoChessScene_431799129079809] RoundStart board data: player:10106021303 heroes:28
[AutoChessScene_431799129079809] RoundStart board data: player:90000080171 heroes:30
[AutoChessScene_431799129079809] Sending RoundStart to Player 10106021303 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431799129079809] RoundStart board data: player:10106021303 heroes:28
[AutoChessScene_431799129079809] RoundStart board data: player:90000080171 heroes:30
[AutoChessScene_431799129079809] Sending RoundStart to Player 90000080171 on GameServer 10102
[AutoChessScene_431799129079809] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431799129079809] Successfully sent RoundStart notifications to all 2 players via NATS
[BattleService] Updated battle 431799129079809 state to StateRoundStart
[AutoChessScene_431799129079809] State change sent to GameServer: StateRoundSettlement -> StateRoundStart (R7)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleStateManager_431799129079809] ===== ROUND 7 INITIALIZATION COMPLETE =====
[BattleStateManager_431799129079809] State: StateRoundStart -> StatePreparation (R7, 65000ms)
[AutoChessScene_431799129079809] Preparation phase started
[PlayerManager_431799129079809] Player 90000080171 ready status set to True
[AutoChessScene_431799129079809] Auto-ready 1 additional bots
[AutoChessScene_431799129079809] Free operation phase started
[BattleService] Updated battle 431799129079809 state to StatePreparation
[AutoChessScene_431799129079809] State change sent to GameServer: StateRoundStart -> StatePreparation (R7)
[BattleStateManager_431799129079809] BattleStateChangedEvent published successfully
[BattleService] LeaveBattle request received from player 10106021303
[BattleService] Player 10106021303 logout, cleaning up battle 431799129079809
[BattleService] Only one real player in battle 431799129079809, cleaning up entire battle
[BattleService] Starting cleanup for battle 431799129079809
[BattleService] Removed battle state for 431799129079809
[CheckerBoard_431799129079809] Cleared all entities
[BuffManager_431799129079809] Cleared all buffs
[AutoChessScene_431799129079809] Scene resources disposed
[SceneManager] Removed AutoChessScene 431799129079809 from thread management
[BattleService] Cleaned up scene for battle 431799129079809
[BattleService] Cleanup completed for battle 431799129079809
[BattleService] LeaveBattle completed successfully for player 10106021303
[NatsServer] Received publish message on subject: /30001/natsrpc.BattleService/LeaveBattle, no reply needed


